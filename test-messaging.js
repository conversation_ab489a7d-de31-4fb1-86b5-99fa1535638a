// Test messaging endpoints
async function testMessaging() {
  console.log('🧪 Testing messaging endpoints...\n');

  const baseUrl = 'http://localhost:3000/api/trpc';

  // Test sendMessage endpoint using proper tRPC batch format
  try {
    console.log('Testing sendMessage endpoint...');
    const response = await fetch(`${baseUrl}/messages.sendMessage`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        "0": {
          "json": {
            "content": "Test message",
            "recipientId": "test-recipient"
          }
        }
      })
    });

    const data = await response.json();
    console.log('✅ sendMessage response:', JSON.stringify(data, null, 2));
  } catch (error) {
    console.log('❌ sendMessage failed:', error.message);
  }

  // Test getMessages endpoint
  try {
    console.log('\nTesting getMessages endpoint...');
    const url = new URL(`${baseUrl}/messages.getMessages`);
    url.searchParams.set('batch', '1');
    url.searchParams.set('input', JSON.stringify({"0":{"json":{"threadId":"test-thread"}}}));

    const response = await fetch(url.toString(), {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      }
    });

    const data = await response.json();
    console.log('✅ getMessages response:', JSON.stringify(data, null, 2));
  } catch (error) {
    console.log('❌ getMessages failed:', error.message);
  }

  console.log('\n🎉 Messaging test complete!');
}

testMessaging();
