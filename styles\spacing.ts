import { StyleSheet } from 'react-native';

// TrainFit Mobile UI Design System - Spacing
export const spacing = StyleSheet.create({
  // Margin and padding values
  xs: {
    margin: 4,
    padding: 4,
  },
  sm: {
    margin: 8,
    padding: 8,
  },
  md: {
    margin: 16,
    padding: 16,
  },
  lg: {
    margin: 24,
    padding: 24,
  },
  xl: {
    margin: 32,
    padding: 32,
  },
  xxl: {
    margin: 48,
    padding: 48,
  },
  
  // Specific margin directions
  marginTop: {
    xs: { marginTop: 4 },
    sm: { marginTop: 8 },
    md: { marginTop: 16 },
    lg: { marginTop: 24 },
    xl: { marginTop: 32 },
  },
  marginBottom: {
    xs: { marginBottom: 4 },
    sm: { marginBottom: 8 },
    md: { marginBottom: 16 },
    lg: { marginBottom: 24 },
    xl: { marginBottom: 32 },
  },
  marginLeft: {
    xs: { marginLeft: 4 },
    sm: { marginLeft: 8 },
    md: { marginLeft: 16 },
    lg: { marginLeft: 24 },
    xl: { marginLeft: 32 },
  },
  marginRight: {
    xs: { marginRight: 4 },
    sm: { marginRight: 8 },
    md: { marginRight: 16 },
    lg: { marginRight: 24 },
    xl: { marginRight: 32 },
  },
  
  // Specific padding directions
  paddingTop: {
    xs: { paddingTop: 4 },
    sm: { paddingTop: 8 },
    md: { paddingTop: 16 },
    lg: { paddingTop: 24 },
    xl: { paddingTop: 32 },
  },
  paddingBottom: {
    xs: { paddingBottom: 4 },
    sm: { paddingBottom: 8 },
    md: { paddingBottom: 16 },
    lg: { paddingBottom: 24 },
    xl: { paddingBottom: 32 },
  },
  paddingLeft: {
    xs: { paddingLeft: 4 },
    sm: { paddingLeft: 8 },
    md: { paddingLeft: 16 },
    lg: { paddingLeft: 24 },
    xl: { paddingLeft: 32 },
  },
  paddingRight: {
    xs: { paddingRight: 4 },
    sm: { paddingRight: 8 },
    md: { paddingRight: 16 },
    lg: { paddingRight: 24 },
    xl: { paddingRight: 32 },
  },
  
  // Horizontal and vertical spacing
  paddingHorizontal: {
    xs: { paddingHorizontal: 4 },
    sm: { paddingHorizontal: 8 },
    md: { paddingHorizontal: 16 },
    lg: { paddingHorizontal: 24 },
    xl: { paddingHorizontal: 32 },
  },
  paddingVertical: {
    xs: { paddingVertical: 4 },
    sm: { paddingVertical: 8 },
    md: { paddingVertical: 16 },
    lg: { paddingVertical: 24 },
    xl: { paddingVertical: 32 },
  },
  marginHorizontal: {
    xs: { marginHorizontal: 4 },
    sm: { marginHorizontal: 8 },
    md: { marginHorizontal: 16 },
    lg: { marginHorizontal: 24 },
    xl: { marginHorizontal: 32 },
  },
  marginVertical: {
    xs: { marginVertical: 4 },
    sm: { marginVertical: 8 },
    md: { marginVertical: 16 },
    lg: { marginVertical: 24 },
    xl: { marginVertical: 32 },
  },
  
  // Layout spacing
  screenPadding: {
    padding: 16,
  },
  sectionSpacing: {
    marginBottom: 24,
  },
  itemSpacing: {
    marginBottom: 16,
  },
  rowSpacing: {
    marginBottom: 12,
  },
});