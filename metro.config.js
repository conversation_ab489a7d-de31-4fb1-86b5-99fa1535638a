const { getDefaultConfig } = require('expo/metro-config');

/** @type {import('expo/metro-config').MetroConfig} */
const config = getDefaultConfig(__dirname);

// Add Node.js polyfills for React Native
config.resolver.alias = {
  ...config.resolver.alias,
  // Polyfill Node.js modules for React Native
  'events': require.resolve('events'),
  'stream': require.resolve('readable-stream'),
  'util': require.resolve('util'),
  'buffer': require.resolve('buffer'),
  'crypto': require.resolve('react-native-crypto-js'),
  'http': require.resolve('stream-http'),
  'https': require.resolve('https-browserify'),
  'os': require.resolve('react-native-os'),
  'url': require.resolve('url'),
  'fs': false,
  'net': false,
  'tls': false,
  'ws': false, // Disable WebSocket library that causes issues
};

// Block problematic modules
config.resolver.blockList = [
  /node_modules\/ws\/lib\/websocket\.js$/,
];

// Add polyfills to the resolver
config.resolver.platforms = ['ios', 'android', 'native', 'web'];

module.exports = config;
