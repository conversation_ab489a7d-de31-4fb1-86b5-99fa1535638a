{"buildCommand": "npm run build", "outputDirectory": "dist", "devCommand": "npm run dev", "cleanUrls": true, "framework": null, "functions": {"api/**/*.ts": {"runtime": "nodejs20.x"}}, "rewrites": [{"source": "/api/(.*)", "destination": "/api/index"}, {"source": "/((?!api).*)", "destination": "/"}], "headers": [{"source": "/api/(.*)", "headers": [{"key": "Access-Control-Allow-Origin", "value": "*"}, {"key": "Access-Control-Allow-Methods", "value": "GET, POST, PUT, DELETE, OPTIONS"}, {"key": "Access-Control-Allow-Headers", "value": "Content-Type, Authorization"}]}]}