{"expo": {"name": "TrainFit", "slug": "trainfitconnections", "version": "1.0.1", "orientation": "portrait", "icon": "./assets/images/icon.png", "scheme": "myapp", "userInterfaceStyle": "automatic", "newArchEnabled": true, "splash": {"image": "./assets/images/splash-icon.png", "resizeMode": "contain", "backgroundColor": "#ffffff"}, "ios": {"supportsTablet": true, "bundleIdentifier": "app.rork.trainfit-connections", "infoPlist": {"ITSAppUsesNonExemptEncryption": false, "NSCameraUsageDescription": "This app uses the camera to let you take profile and progress photos, and to scan QR codes for workouts or classes.", "NSPhotoLibraryUsageDescription": "This app accesses your photo library so you can upload progress pictures and personalize your profile."}}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/images/adaptive-icon.png", "backgroundColor": "#ffffff"}, "package": "app.rork.trainfit_connections"}, "web": {"favicon": "./assets/images/favicon.png"}, "plugins": [["expo-router", {"origin": "https://rork.app/"}]], "experiments": {"typedRoutes": true}, "extra": {"router": {"origin": "https://rork.app/"}, "eas": {"projectId": "11cd59da-7e1d-451c-a74b-66968ff0d617"}}}}