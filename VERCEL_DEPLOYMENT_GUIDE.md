# 🚀 Vercel Deployment Guide for TrainFit Connections

This guide will help you deploy your TrainFit Connections app to Vercel for production use.

## 🏗️ **DEPLOYMENT ARCHITECTURE**

Your app consists of two parts that will be deployed together:

1. **Frontend**: Expo React Native web build (static files)
2. **Backend**: Hono server as Vercel serverless functions

## 📋 **PREREQUISITES**

1. **Vercel Account**: Sign up at [vercel.com](https://vercel.com)
2. **GitHub Repository**: Your code should be in a GitHub repository
3. **Supabase Project**: Already configured ✅
4. **Environment Variables**: Ready to configure

## 🚀 **DEPLOYMENT STEPS**

### **Step 1: Connect to Vercel**

1. Go to [vercel.com](https://vercel.com) and sign in
2. Click "New Project"
3. Import your GitHub repository
4. Select "TrainFit Connections" repository

### **Step 2: Configure Project Settings**

In Vercel dashboard:

1. **Framework Preset**: Select "Other"
2. **Build Command**: `npm run build` (already configured)
3. **Output Directory**: `dist` (already configured)
4. **Install Command**: `npm install` (default)

### **Step 3: Environment Variables**

Add these environment variables in Vercel dashboard:

```bash
# Supabase Configuration
SUPABASE_URL=https://iuwlgzguabqymurvcqub.supabase.co
SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Iml1d2xnemd1YWJxeW11cnZjcXViIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDgzNzgzNzAsImV4cCI6MjA2Mzk1NDM3MH0.rbu4bgO23kZDjwNgBPCO5ilR3IA4XJ_5d2tOWJ6PVss

# Production Configuration
NODE_ENV=production
PORT=3000
```

### **Step 4: Deploy**

1. Click "Deploy" in Vercel
2. Wait for build to complete (2-3 minutes)
3. Your app will be available at: `https://your-app-name.vercel.app`

## 🔧 **CONFIGURATION FILES**

### **vercel.json** ✅ (Already configured)
```json
{
  "buildCommand": "npm run build",
  "outputDirectory": "dist",
  "functions": {
    "api/**/*.ts": {
      "runtime": "nodejs20.x"
    }
  },
  "rewrites": [
    {
      "source": "/api/(.*)",
      "destination": "/api/index"
    },
    {
      "source": "/((?!api).*)",
      "destination": "/"
    }
  ]
}
```

### **api/index.ts** ✅ (Already created)
```typescript
import { app } from '../backend/hono';
export default app.fetch;
```

## 📱 **MOBILE APP CONFIGURATION**

### **For Production Builds**

Update your mobile app to use the production API:

1. **EAS Build**: Use `expo build` or EAS Build service
2. **Environment**: Set `NODE_ENV=production`
3. **API URL**: Will automatically use `https://your-app-name.vercel.app/api`

### **For Development**

- **Expo Go**: Continue using development server
- **API URL**: Will automatically use local development server

## 🧪 **TESTING DEPLOYMENT**

### **1. Health Check**
```bash
curl https://your-app-name.vercel.app/api/health
```

Expected response:
```json
{
  "status": "ok",
  "timestamp": "2024-01-28T...",
  "services": {
    "api": {"status": "healthy"},
    "database": {"status": "healthy"},
    "supabase": {"status": "connected"}
  }
}
```

### **2. WebSocket Test**
```bash
curl https://your-app-name.vercel.app/api/websocket/test
```

### **3. Web App Test**
Visit: `https://your-app-name.vercel.app`

### **4. Mobile App Test**
- Build production version with EAS
- Test messaging functionality
- Verify real-time updates work

## 🔄 **CONTINUOUS DEPLOYMENT**

Vercel automatically deploys when you push to your main branch:

1. **Push to GitHub**: `git push origin main`
2. **Auto Deploy**: Vercel detects changes and deploys
3. **Live in 2-3 minutes**: Your changes are live

## 🛠️ **TROUBLESHOOTING**

### **Build Failures**

1. **Check Build Logs**: In Vercel dashboard
2. **Common Issues**:
   - Missing dependencies: `npm install`
   - TypeScript errors: Fix in your IDE
   - Environment variables: Check they're set correctly

### **API Issues**

1. **Check Function Logs**: In Vercel dashboard
2. **Common Issues**:
   - Supabase connection: Verify environment variables
   - CORS errors: Already configured in `vercel.json`
   - Timeout: Vercel functions have 10-second limit

### **Mobile App Issues**

1. **API Connection**: Verify production URL is correct
2. **Environment**: Ensure `NODE_ENV=production` is set
3. **Network**: Check mobile device has internet access

## 📊 **MONITORING & ANALYTICS**

### **Vercel Dashboard**
- **Function Invocations**: Monitor API usage
- **Build History**: Track deployments
- **Performance**: Response times and errors

### **Supabase Dashboard**
- **Database Usage**: Monitor queries and connections
- **Real-time**: Check WebSocket connections
- **Authentication**: User login/logout events

## 🔐 **SECURITY CONSIDERATIONS**

### **Environment Variables**
- ✅ **Supabase Keys**: Stored securely in Vercel
- ✅ **CORS**: Configured for your domain
- ✅ **HTTPS**: Automatic SSL certificates

### **Database Security**
- ✅ **Row Level Security**: Enabled on Supabase
- ✅ **API Keys**: Anon key is safe for client use
- ✅ **User Isolation**: Users only see their data

## 🎯 **PERFORMANCE OPTIMIZATION**

### **Vercel Edge Functions**
- **Global CDN**: Fast response times worldwide
- **Automatic Scaling**: Handles traffic spikes
- **Cold Start**: ~100ms for first request

### **Supabase Performance**
- **Connection Pooling**: Efficient database connections
- **Caching**: Built-in query caching
- **Real-time**: Optimized WebSocket connections

## 📈 **SCALING CONSIDERATIONS**

### **Current Limits**
- **Vercel Free**: 100GB bandwidth, 100 function invocations/day
- **Vercel Pro**: Unlimited bandwidth, 1000 function invocations/day
- **Supabase Free**: 500MB database, 2GB bandwidth

### **Upgrade Path**
1. **Vercel Pro**: $20/month for production apps
2. **Supabase Pro**: $25/month for larger databases
3. **Custom Domain**: Configure in Vercel dashboard

## ✅ **DEPLOYMENT CHECKLIST**

- [ ] GitHub repository connected to Vercel
- [ ] Environment variables configured
- [ ] Build command set to `npm run build`
- [ ] Output directory set to `dist`
- [ ] Supabase connection tested
- [ ] Health check endpoint working
- [ ] Web app loads correctly
- [ ] Mobile app connects to production API
- [ ] Real-time messaging working
- [ ] Custom domain configured (optional)

---

**🎉 Your TrainFit Connections app is now ready for production deployment on Vercel!**

**Next Steps:**
1. Deploy to Vercel following the steps above
2. Test all functionality in production
3. Update mobile app builds to use production API
4. Monitor performance and usage in dashboards
