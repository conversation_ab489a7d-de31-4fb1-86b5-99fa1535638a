import { createClient } from '@supabase/supabase-js';
import Constants from 'expo-constants';

// Get Supabase configuration from environment variables or fallback to hardcoded values
const supabaseUrl = Constants.expoConfig?.extra?.supabaseUrl || 'https://iuwlgzguabqymurvcqub.supabase.co';
const supabaseAnonKey = Constants.expoConfig?.extra?.supabaseAnonKey || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Iml1d2xnemd1YWJxeW11cnZjcXViIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDgzNzgzNzAsImV4cCI6MjA2Mzk1NDM3MH0.rbu4bgO23kZDjwNgBPCO5ilR3IA4XJ_5d2tOWJ6PVss';

export const supabase = createClient(supabaseUrl, supabaseAnonKey, {
  realtime: {
    // Configure realtime to work with React Native WebSocket
    params: {
      eventsPerSecond: 10,
    },
    // Use React Native's built-in WebSocket
    transport: WebSocket,
    timeout: 20000,
    heartbeatIntervalMs: 30000,
  },
  global: {
    headers: {
      'X-Client-Info': 'trainfit-connections@1.0.0',
    },
  },
});
