import 'dotenv/config';
import { serve } from '@hono/node-server';
import { app } from './backend/hono';

const port = parseInt(process.env.PORT || '3000', 10);

console.log(`🚀 Starting server on port ${port}...`);

serve({
  fetch: app.fetch,
  port: port,
}, (info) => {
  console.log(`✅ Server is running on http://localhost:${info.port}`);
  console.log(`📱 API endpoints available at:`);
  console.log(`   - Health check: http://localhost:${info.port}/api/health`);
  console.log(`   - tRPC: http://localhost:${info.port}/api/trpc`);
  console.log(`   - Example: http://localhost:${info.port}/api/trpc/example.hi`);
  console.log(`\n🔗 For Expo Go testing:`);
  console.log(`   - Make sure your phone and computer are on the same network`);
  console.log(`   - The app will automatically detect your IP address`);
  console.log(`   - If you have connection issues, check your firewall settings`);
});
