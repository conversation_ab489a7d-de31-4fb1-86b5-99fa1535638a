// WebSocket polyfill for React Native
// This replaces the Node.js 'ws' library with React Native's built-in WebSocket

import { EventEmitter } from 'events';

class WebSocketPolyfill extends EventEmitter {
  constructor(url, protocols, options = {}) {
    super();
    
    this.url = url;
    this.protocols = protocols;
    this.readyState = WebSocket.CONNECTING;
    this.bufferedAmount = 0;
    
    // Create the actual WebSocket using React Native's built-in WebSocket
    this._ws = new WebSocket(url, protocols);
    
    // Forward events from the native WebSocket to our EventEmitter
    this._ws.onopen = (event) => {
      this.readyState = WebSocket.OPEN;
      this.emit('open', event);
      if (this.onopen) this.onopen(event);
    };
    
    this._ws.onmessage = (event) => {
      this.emit('message', event.data, false); // false for isBinary
      if (this.onmessage) this.onmessage(event);
    };
    
    this._ws.onerror = (event) => {
      this.emit('error', event.error || new Error('WebSocket error'));
      if (this.onerror) this.onerror(event);
    };
    
    this._ws.onclose = (event) => {
      this.readyState = WebSocket.CLOSED;
      this.emit('close', event.code, event.reason);
      if (this.onclose) this.onclose(event);
    };
  }
  
  send(data) {
    if (this.readyState === WebSocket.OPEN) {
      this._ws.send(data);
    } else {
      throw new Error('WebSocket is not open');
    }
  }
  
  close(code, reason) {
    if (this.readyState === WebSocket.OPEN || this.readyState === WebSocket.CONNECTING) {
      this.readyState = WebSocket.CLOSING;
      this._ws.close(code, reason);
    }
  }
  
  ping(data, mask, callback) {
    // React Native WebSocket doesn't support ping/pong, so we'll just call the callback
    if (typeof callback === 'function') {
      setTimeout(callback, 0);
    }
  }
  
  pong(data, mask, callback) {
    // React Native WebSocket doesn't support ping/pong, so we'll just call the callback
    if (typeof callback === 'function') {
      setTimeout(callback, 0);
    }
  }
  
  terminate() {
    this.close();
  }
}

// Add WebSocket constants
WebSocketPolyfill.CONNECTING = 0;
WebSocketPolyfill.OPEN = 1;
WebSocketPolyfill.CLOSING = 2;
WebSocketPolyfill.CLOSED = 3;

// Server class for compatibility (not used in React Native)
class WebSocketServer extends EventEmitter {
  constructor(options, callback) {
    super();
    console.warn('WebSocketServer is not supported in React Native');
    if (callback) callback();
  }
  
  close(callback) {
    if (callback) callback();
  }
}

// Export the polyfill
module.exports = WebSocketPolyfill;
module.exports.WebSocket = WebSocketPolyfill;
module.exports.WebSocketServer = WebSocketServer;
module.exports.default = WebSocketPolyfill;
