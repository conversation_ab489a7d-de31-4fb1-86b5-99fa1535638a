import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Trainer, Session, WorkoutPlan, MealPlan, Reminder, Client, Photo, Payment, Payout, PaymentMethod, Revenue } from '@/types';
import { Alert } from 'react-native';
import { supabase } from '@/lib/supabase';
import { useAuthStore } from './auth-store';

interface TrainerState {
  clients: Client[];
  sessions: Session[];
  workoutPlans: WorkoutPlan[];
  mealPlans: MealPlan[];
  reminders: Reminder[];
  photos: Photo[];
  payments: Payment[];
  payouts: Payout[];
  revenue: Revenue;
  isLoading: boolean;
  error: string | null;
  
  // Client management
  addClient: (client: Client) => Promise<void>;
  updateClient: (client: Client) => Promise<void>;
  removeClient: (clientId: string) => Promise<void>;
  
  // Session management
  scheduleSession: (session: Omit<Session, 'id'>) => Promise<Session>;
  updateSession: (sessionId: string, updates: Partial<Session>) => Promise<Session | undefined>;
  cancelSession: (sessionId: string) => Promise<void>;
  acceptSession: (sessionId: string, isNewClient?: boolean) => Promise<void>;
  declineSession: (sessionId: string, reason?: string) => Promise<void>;
  
  // Workout plans
  createWorkoutPlan: (plan: Omit<WorkoutPlan, 'id' | 'createdAt'>) => void;
  updateWorkoutPlan: (planId: string, updates: Partial<WorkoutPlan>) => void;
  deleteWorkoutPlan: (planId: string) => void;
  
  // Meal plans
  createMealPlan: (plan: Omit<MealPlan, 'id' | 'createdAt'>) => void;
  updateMealPlan: (planId: string, updates: Partial<MealPlan>) => void;
  deleteMealPlan: (planId: string) => void;
  
  // Reminders
  createReminder: (reminder: Omit<Reminder, 'id'>) => Promise<Reminder>;
  updateReminder: (reminderId: string, updates: Partial<Reminder>) => Promise<void>;
  deleteReminder: (reminderId: string) => Promise<void>;
  markReminderAsRead: (reminderId: string) => Promise<void>;
  
  // Photos
  addPhoto: (photo: Omit<Photo, 'id' | 'createdAt' | 'likes'>) => void;
  updatePhoto: (photoId: string, updates: Partial<Photo>) => void;
  deletePhoto: (photoId: string) => void;
  likePhoto: (photoId: string) => void;
  
  // Payments
  recordPayment: (payment: Omit<Payment, 'id' | 'date'>) => void;
  updatePaymentStatus: (paymentId: string, status: 'pending' | 'completed' | 'failed' | 'refunded') => void;
  fetchPayments: () => Promise<void>;
  
  // Payouts
  requestPayout: (amount: number, paymentMethodId: string) => Promise<Payout>;
  fetchPayouts: () => Promise<void>;
  
  // Revenue
  fetchRevenue: (period?: 'day' | 'week' | 'month' | 'year' | 'all') => Promise<void>;
  generateRevenueForUser: (userId: string) => Promise<void>;

  // Real-time listeners for trainer-side data
  fetchSessions: () => Promise<void>;
  fetchReminders: () => Promise<void>;
  fetchPhotos: () => Promise<void>;
  subscribeToRealtime: () => () => void;
}

// Mock payment methods
const mockPaymentMethods: PaymentMethod[] = [
  {
    id: 'pm1',
    type: 'bank',
    name: 'Chase Bank',
    last4: '4567',
    isDefault: true,
  },
  {
    id: 'pm2',
    type: 'card',
    name: 'Visa',
    last4: '8901',
    isDefault: false,
  }
];

// Mock reminders data
const mockReminders: Reminder[] = [
  {
    id: 'tr1',
    trainerId: 't1',
    clientId: 'c1',
    title: 'Session Request',
    message: "You have a new session request from Emma Wilson for tomorrow at 10:00 AM. Please accept or decline.",
    date: new Date().toISOString(),
    time: '09:15',
    isRead: false,
    type: 'session_request',
    relatedId: 's3'
  },
  {
    id: 'tr2',
    trainerId: 't1',
    clientId: 'c2',
    title: 'Client Goal Update',
    message: "Michael has updated his fitness goals. He now wants to focus more on strength training.",
    date: new Date(Date.now() - ********).toISOString(), // Yesterday
    time: '14:30',
    isRead: true,
  },
  {
    id: 'tr3',
    trainerId: 't1',
    clientId: 'c1',
    title: 'Payment Received',
    message: "You've received a payment of $75.00 from Emma Wilson for the session on May 10, 2023.",
    date: new Date(Date.now() - 172800000).toISOString(), // 2 days ago
    time: '16:45',
    isRead: true,
    type: 'other',
  },
  {
    id: 'tr4',
    trainerId: 't1',
    clientId: 'c1',
    title: 'Training Tip: Client Motivation',
    message: "Research shows that clients who receive positive feedback during sessions are 30% more likely to stick with their fitness program. Try acknowledging small improvements in form or endurance.",
    date: new Date(Date.now() - 259200000).toISOString(), // 3 days ago
    time: '08:30',
    isRead: false,
    type: 'tip',
  },
  {
    id: 'tr5',
    trainerId: 't1',
    clientId: 'c1',
    title: 'Business Growth Insight',
    message: "Trainers who post weekly transformation photos see 40% higher client engagement. Consider adding before/after photos to your profile (with client permission).",
    date: new Date(Date.now() - 345600000).toISOString(), // 4 days ago
    time: '10:15',
    isRead: false,
    type: 'tip',
  },
];

// Create a session for today
const today = new Date();
const todayString = today.toISOString();

// Mock sessions data
const mockSessions: Session[] = [
  // Today's session
  {
    id: 's1',
    trainerId: 't1',
    clientId: 'c1',
    date: todayString,
    startTime: '15:00',
    endTime: '16:00',
    status: 'scheduled',
    type: 'one-on-one',
    notes: 'Focus on upper body strength',
    location: {
      latitude: 30.2672,
      longitude: -97.7431,
      address: 'Austin Fitness Center, 123 Main St'
    },
    cost: 75,
    paymentStatus: 'paid',
    paymentMethod: 'credit_card'
  },
  // Tomorrow's session
  {
    id: 's2',
    trainerId: 't1',
    clientId: 'c2',
    date: new Date(today.getTime() + 24 * 60 * 60 * 1000).toISOString(),
    startTime: '10:00',
    endTime: '11:00',
    status: 'scheduled',
    type: 'virtual',
    notes: 'HIIT workout session',
    cost: 65,
    paymentStatus: 'pending',
    paymentMethod: 'paypal'
  },
  // Pending session
  {
    id: 's3',
    trainerId: 't1',
    clientId: 'c1',
    date: new Date(today.getTime() + 2 * 24 * 60 * 60 * 1000).toISOString(),
    startTime: '14:00',
    endTime: '15:00',
    status: 'pending',
    type: 'one-on-one',
    notes: 'First session - focus on assessment and goal setting',
    location: {
      latitude: 30.2672,
      longitude: -97.7431,
      address: 'Austin Fitness Center, 123 Main St'
    },
    cost: 75,
    paymentStatus: 'pending',
    paymentMethod: 'credit_card'
  },
  // Today's second session
  {
    id: 's4',
    trainerId: 't1',
    clientId: 'c3',
    date: todayString,
    startTime: '18:00',
    endTime: '19:00',
    status: 'scheduled',
    type: 'one-on-one',
    notes: 'Flexibility and mobility work',
    location: {
      latitude: 30.2672,
      longitude: -97.7431,
      address: 'Austin Fitness Center, 123 Main St'
    },
    cost: 75,
    paymentStatus: 'pending',
    paymentMethod: 'credit_card'
  },
  // Past session
  {
    id: 's5',
    trainerId: 't1',
    clientId: 'c2',
    date: new Date(today.getTime() - 2 * 24 * 60 * 60 * 1000).toISOString(),
    startTime: '09:00',
    endTime: '10:00',
    status: 'completed',
    type: 'one-on-one',
    notes: 'Strength training session',
    location: {
      latitude: 30.2672,
      longitude: -97.7431,
      address: 'Austin Fitness Center, 123 Main St'
    },
    cost: 75,
    paymentStatus: 'paid',
    paymentMethod: 'credit_card'
  },
  // Group session
  {
    id: 's6',
    trainerId: 't1',
    clientId: 'c3',
    date: new Date(today.getTime() + 3 * 24 * 60 * 60 * 1000).toISOString(),
    startTime: '17:00',
    endTime: '18:00',
    status: 'scheduled',
    type: 'group',
    participantCount: 5,
    notes: 'Group HIIT session',
    location: {
      latitude: 30.2672,
      longitude: -97.7431,
      address: 'Austin Fitness Center, 123 Main St'
    },
    cost: 45,
    paymentStatus: 'pending',
    paymentMethod: 'credit_card'
  },
  // House call session
  {
    id: 's7',
    trainerId: 't1',
    clientId: 'c1',
    date: new Date(today.getTime() + 5 * 24 * 60 * 60 * 1000).toISOString(),
    startTime: '08:00',
    endTime: '09:00',
    status: 'scheduled',
    type: 'house-call',
    notes: 'In-home training session',
    location: {
      latitude: 30.3072,
      longitude: -97.7331,
      address: 'Client Home Address'
    },
    cost: 90,
    paymentStatus: 'pending',
    paymentMethod: 'credit_card'
  }
];

// Mock clients data
const mockClients: Client[] = [
  {
    id: 'c1',
    name: 'Emma Wilson',
    email: '<EMAIL>',
    role: 'client',
    profileImage: 'https://images.unsplash.com/photo-1494790108377-be9c29b29330?q=80&w=1000',
    goals: ['Weight Loss', 'Toning'],
    fitnessLevel: 'intermediate',
    location: {
      latitude: 30.2672,
      longitude: -97.7431,
      address: 'Austin, TX'
    },
    healthInfo: {
      height: 165,
      weight: 62,
      medicalConditions: ['None']
    },
    sessionCount: 5,
    bio: 'Emma is a marketing professional looking to improve her fitness and lose weight. She enjoys outdoor activities and has been consistent with her training schedule.'
  },
  {
    id: 'c2',
    name: 'Michael Brown',
    email: '<EMAIL>',
    role: 'client',
    profileImage: 'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?q=80&w=1000',
    goals: ['Muscle Gain', 'Strength'],
    fitnessLevel: 'advanced',
    location: {
      latitude: 30.2982,
      longitude: -97.7431,
      address: 'Austin, TX'
    },
    healthInfo: {
      height: 180,
      weight: 75,
      medicalConditions: ['None']
    },
    sessionCount: 3,
    bio: 'Michael is a software engineer who has been training for several years. He is focused on building strength and muscle mass, and is dedicated to his nutrition plan.'
  },
  {
    id: 'c3',
    name: 'Sophia Chen',
    email: '<EMAIL>',
    role: 'client',
    profileImage: 'https://images.unsplash.com/photo-1580489944761-15a19d654956?q=80&w=1000',
    goals: ['Flexibility', 'Posture'],
    fitnessLevel: 'beginner',
    location: {
      latitude: 30.2512,
      longitude: -97.7531,
      address: 'Austin, TX'
    },
    healthInfo: {
      height: 160,
      weight: 55,
      medicalConditions: ['Mild back pain']
    },
    sessionCount: 0,
    bio: 'Sophia is new to fitness training and is primarily focused on improving her posture and flexibility. She works as a graphic designer and spends long hours at her desk.'
  },
];

// Function to generate random revenue data for a specific trainer
const generateRevenueData = (trainerId: string): Revenue => {
  // Random values for earnings
  const totalEarnings = Math.floor(1500 + Math.random() * 3000);
  const availableBalance = Math.floor(totalEarnings * 0.7);
  const pendingPayouts = Math.floor(totalEarnings * 0.3);
  
  // Current month stats
  const currentMonthEarnings = Math.floor(300 + Math.random() * 800);
  const lastMonthEarnings = Math.floor(250 + Math.random() * 700);
  const growth = parseFloat(((currentMonthEarnings / lastMonthEarnings - 1) * 100).toFixed(1));
  
  // Session types breakdown
  const oneOnOneAmount = Math.floor(totalEarnings * (0.6 + Math.random() * 0.2));
  const groupAmount = Math.floor(totalEarnings * (0.1 + Math.random() * 0.2));
  const virtualAmount = totalEarnings - oneOnOneAmount - groupAmount;
  
  // Calculate percentages
  const oneOnOnePercentage = parseFloat(((oneOnOneAmount / totalEarnings) * 100).toFixed(1));
  const groupPercentage = parseFloat(((groupAmount / totalEarnings) * 100).toFixed(1));
  const virtualPercentage = parseFloat(((virtualAmount / totalEarnings) * 100).toFixed(1));
  
  // Generate monthly revenue data
  const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'];
  const monthlyRevenue = months.map(month => ({
    month,
    amount: Math.floor(200 + Math.random() * 800)
  }));
  
  return {
    totalEarnings,
    availableBalance,
    pendingPayouts,
    currentMonth: {
      earnings: currentMonthEarnings,
      sessionsCompleted: Math.floor(5 + Math.random() * 15),
      clientCount: Math.floor(2 + Math.random() * 8),
      growth
    },
    lastMonth: {
      earnings: lastMonthEarnings,
      sessionsCompleted: Math.floor(4 + Math.random() * 12),
      clientCount: Math.floor(2 + Math.random() * 6),
      growth: parseFloat((Math.random() * 15).toFixed(1))
    },
    revenueByType: [
      { type: 'one-on-one', amount: oneOnOneAmount, percentage: oneOnOnePercentage },
      { type: 'group', amount: groupAmount, percentage: groupPercentage },
      { type: 'virtual', amount: virtualAmount, percentage: virtualPercentage },
    ],
    monthlyRevenue
  };
};

// Function to generate random payment data for a specific trainer
const generatePaymentData = (trainerId: string, clientIds: string[]): Payment[] => {
  const payments: Payment[] = [];
  const paymentCount = Math.floor(3 + Math.random() * 8); // 3-10 payments
  
  for (let i = 0; i < paymentCount; i++) {
    const clientId = clientIds[Math.floor(Math.random() * clientIds.length)];
    const amount = Math.floor(50 + Math.random() * 100);
    const daysAgo = Math.floor(Math.random() * 30);
    const status = Math.random() > 0.2 ? 'completed' : 'pending';
    const method = Math.random() > 0.5 ? 'credit_card' : 'paypal';
    
    payments.push({
      id: `p${trainerId}_${i}`,
      trainerId,
      clientId,
      amount,
      status: status as 'completed' | 'pending' | 'failed' | 'refunded',
      method: method as 'credit_card' | 'paypal' | 'bank' | 'cash',
      date: new Date(Date.now() - daysAgo * 24 * 60 * 60 * 1000).toISOString(),
      transactionId: `txn_${Math.random().toString(36).substring(2, 15)}`
    });
  }
  
  return payments;
};

// Function to generate training tips for trainers
const generateTrainingTips = (trainerId: string): Reminder[] => {
  const tips = [
    {
      title: "Client Retention Tip",
      message: "Clients who receive a follow-up message within 24 hours after their session are 40% more likely to book again. Try sending a quick note after each session."
    },
    {
      title: "Business Growth Strategy",
      message: "Offering a free fitness assessment to new clients can increase conversion rates by up to 60%. Consider adding this to your service offerings."
    },
    {
      title: "Training Effectiveness",
      message: "Research shows that clients achieve 30% better results when they track their workouts. Encourage your clients to use a fitness tracking app between sessions."
    },
    {
      title: "Client Motivation Tip",
      message: "Setting specific, measurable goals with clients increases their motivation by 70%. Try establishing clear 30, 60, and 90-day milestones with each client."
    },
    {
      title: "Nutrition Coaching",
      message: "Trainers who provide basic nutrition guidance see 45% better client results. Consider getting certified in nutrition coaching to expand your services."
    }
  ];
  
  return tips.map((tip, index) => ({
    id: `tip_${trainerId}_${index}`,
    trainerId,
    clientId: trainerId, // Self-tip
    title: tip.title,
    message: tip.message,
    date: new Date(Date.now() - (index + 1) * ********).toISOString(), // Staggered dates
    time: '08:00',
    isRead: false,
    type: 'tip'
  }));
};

export const useTrainerStore = create<TrainerState>()(
  persist(
    (set, get) => ({
      clients: [],
      sessions: [],
      workoutPlans: [],
      mealPlans: [],
      reminders: [],
      photos: [],
      payments: [],
      payouts: [],
      revenue: {
        totalEarnings: 0,
        availableBalance: 0,
        pendingPayouts: 0,
        currentMonth: { earnings: 0, sessionsCompleted: 0, clientCount: 0, growth: 0 },
        lastMonth: { earnings: 0, sessionsCompleted: 0, clientCount: 0, growth: 0 },
        revenueByType: [],
        monthlyRevenue: []
      },
      isLoading: false,
      error: null,
      
      // Client management
      addClient: async (client) => {
        set({ isLoading: true, error: null });
        try {
          const { data, error } = await supabase
            .from('profiles')
            .insert([{ ...client, role: 'client' }])
            .select()
            .single();
          if (error) throw error;
          set(state => ({ clients: [...state.clients, data], isLoading: false }));
        } catch (error) {
          set({ error: error instanceof Error ? error.message : 'Failed to add client', isLoading: false });
        }
      },
      updateClient: async (client) => {
        set({ isLoading: true, error: null });
        try {
          const { data, error } = await supabase
            .from('profiles')
            .update(client)
            .eq('id', client.id)
            .select()
            .single();
          if (error) throw error;
          set(state => ({
            clients: state.clients.map(c => c.id === client.id ? data : c),
            isLoading: false
          }));
        } catch (error) {
          set({ error: error instanceof Error ? error.message : 'Failed to update client', isLoading: false });
        }
      },
      removeClient: async (clientId) => {
        set({ isLoading: true, error: null });
        try {
          const { error } = await supabase
            .from('profiles')
            .delete()
            .eq('id', clientId);
          if (error) throw error;
          set(state => ({
            clients: state.clients.filter(client => client.id !== clientId),
            isLoading: false
          }));
        } catch (error) {
          set({ error: error instanceof Error ? error.message : 'Failed to remove client', isLoading: false });
        }
      },
      
      // Session management
      scheduleSession: async (sessionData) => {
        set({ isLoading: true, error: null });
        try {
          const { data, error } = await supabase
            .from('sessions')
            .insert([{ ...sessionData, status: 'scheduled' }])
            .select()
            .single();
          if (error) throw error;
          set(state => ({ sessions: [...state.sessions, data], isLoading: false }));
          return data;
        } catch (error) {
          set({ error: error instanceof Error ? error.message : 'Failed to schedule session', isLoading: false });
          throw error;
        }
      },
      updateSession: async (sessionId, updates) => {
        set({ isLoading: true, error: null });
        try {
          const { data, error } = await supabase
            .from('sessions')
            .update(updates)
            .eq('id', sessionId)
            .select()
            .single();
          if (error) throw error;
          set(state => ({
            sessions: state.sessions.map(s => s.id === sessionId ? data : s),
            isLoading: false
          }));
          return data;
        } catch (error) {
          set({ error: error instanceof Error ? error.message : 'Failed to update session', isLoading: false });
          throw error;
        }
      },
      cancelSession: async (sessionId) => {
        set({ isLoading: true, error: null });
        try {
          const { data, error } = await supabase
            .from('sessions')
            .update({ status: 'cancelled' })
            .eq('id', sessionId)
            .select()
            .single();
          if (error) throw error;
          set(state => ({
            sessions: state.sessions.map(s => s.id === sessionId ? data : s),
            isLoading: false
          }));
        } catch (error) {
          set({ error: error instanceof Error ? error.message : 'Failed to cancel session', isLoading: false });
        }
      },
      acceptSession: async (sessionId, isNewClient = false) => {
        set({ isLoading: true, error: null });
        try {
          const { data, error } = await supabase
            .from('sessions')
            .update({ status: 'scheduled', isNewClient })
            .eq('id', sessionId)
            .select()
            .single();
          if (error) throw error;
          set(state => ({
            sessions: state.sessions.map(s => s.id === sessionId ? data : s),
            isLoading: false
          }));
        } catch (error) {
          set({ error: error instanceof Error ? error.message : 'Failed to accept session', isLoading: false });
        }
      },
      declineSession: async (sessionId, reason) => {
        set({ isLoading: true, error: null });
        try {
          const { data, error } = await supabase
            .from('sessions')
            .update({ status: 'declined', declineReason: reason })
            .eq('id', sessionId)
            .select()
            .single();
          if (error) throw error;
          set(state => ({
            sessions: state.sessions.map(s => s.id === sessionId ? data : s),
            isLoading: false
          }));
        } catch (error) {
          set({ error: error instanceof Error ? error.message : 'Failed to decline session', isLoading: false });
        }
      },
      
      // Workout plans
      createWorkoutPlan: async (planData) => {
        set({ isLoading: true, error: null });
        try {
          const { data, error } = await supabase
            .from('workout_plans')
            .insert([{ ...planData }])
            .select()
            .single();
          if (error) throw error;
          set(state => ({ workoutPlans: [...state.workoutPlans, data], isLoading: false }));
          return data;
        } catch (error) {
          set({ error: error instanceof Error ? error.message : 'Failed to create workout plan', isLoading: false });
          throw error;
        }
      },
      updateWorkoutPlan: async (planId, updates) => {
        set({ isLoading: true, error: null });
        try {
          const { data, error } = await supabase
            .from('workout_plans')
            .update(updates)
            .eq('id', planId)
            .select()
            .single();
          if (error) throw error;
          set(state => ({
            workoutPlans: state.workoutPlans.map(p => p.id === planId ? data : p),
            isLoading: false
          }));
          return data;
        } catch (error) {
          set({ error: error instanceof Error ? error.message : 'Failed to update workout plan', isLoading: false });
          throw error;
        }
      },
      deleteWorkoutPlan: async (planId) => {
        set({ isLoading: true, error: null });
        try {
          const { error } = await supabase
            .from('workout_plans')
            .delete()
            .eq('id', planId);
          if (error) throw error;
          set(state => ({
            workoutPlans: state.workoutPlans.filter(plan => plan.id !== planId),
            isLoading: false
          }));
        } catch (error) {
          set({ error: error instanceof Error ? error.message : 'Failed to delete workout plan', isLoading: false });
        }
      },
      
      // Meal plans
      createMealPlan: async (planData) => {
        set({ isLoading: true, error: null });
        try {
          const { data, error } = await supabase
            .from('meal_plans')
            .insert([{ ...planData }])
            .select()
            .single();
          if (error) throw error;
          set(state => ({ mealPlans: [...state.mealPlans, data], isLoading: false }));
          return data;
        } catch (error) {
          set({ error: error instanceof Error ? error.message : 'Failed to create meal plan', isLoading: false });
          throw error;
        }
      },
      updateMealPlan: async (planId, updates) => {
        set({ isLoading: true, error: null });
        try {
          const { data, error } = await supabase
            .from('meal_plans')
            .update(updates)
            .eq('id', planId)
            .select()
            .single();
          if (error) throw error;
          set(state => ({
            mealPlans: state.mealPlans.map(p => p.id === planId ? data : p),
            isLoading: false
          }));
          return data;
        } catch (error) {
          set({ error: error instanceof Error ? error.message : 'Failed to update meal plan', isLoading: false });
          throw error;
        }
      },
      deleteMealPlan: async (planId) => {
        set({ isLoading: true, error: null });
        try {
          const { error } = await supabase
            .from('meal_plans')
            .delete()
            .eq('id', planId);
          if (error) throw error;
          set(state => ({
            mealPlans: state.mealPlans.filter(plan => plan.id !== planId),
            isLoading: false
          }));
        } catch (error) {
          set({ error: error instanceof Error ? error.message : 'Failed to delete meal plan', isLoading: false });
        }
      },
      
      // Reminders
      createReminder: async (reminderData) => {
        set({ isLoading: true, error: null });
        try {
          const { data, error } = await supabase
            .from('reminders')
            .insert([{ ...reminderData }])
            .select()
            .single();
          if (error) throw error;
          set(state => ({ reminders: [...state.reminders, data], isLoading: false }));
          return data;
        } catch (error) {
          set({ error: error instanceof Error ? error.message : 'Failed to create reminder', isLoading: false });
          throw error;
        }
      },
      updateReminder: async (reminderId, updates) => {
        set({ isLoading: true, error: null });
        try {
          const { data, error } = await supabase
            .from('reminders')
            .update(updates)
            .eq('id', reminderId)
            .select()
            .single();
          if (error) throw error;
          set(state => ({
            reminders: state.reminders.map(r => r.id === reminderId ? data : r),
            isLoading: false
          }));
        } catch (error) {
          set({ error: error instanceof Error ? error.message : 'Failed to update reminder', isLoading: false });
        }
      },
      deleteReminder: async (reminderId) => {
        set({ isLoading: true, error: null });
        try {
          const { error } = await supabase
            .from('reminders')
            .delete()
            .eq('id', reminderId);
          if (error) throw error;
          set(state => ({
            reminders: state.reminders.filter(reminder => reminder.id !== reminderId),
            isLoading: false
          }));
        } catch (error) {
          set({ error: error instanceof Error ? error.message : 'Failed to delete reminder', isLoading: false });
        }
      },
      markReminderAsRead: async (reminderId) => {
        set({ isLoading: true, error: null });
        try {
          const { data, error } = await supabase
            .from('reminders')
            .update({ isRead: true })
            .eq('id', reminderId)
            .select()
            .single();
          if (error) throw error;
          set(state => ({
            reminders: state.reminders.map(r => r.id === reminderId ? data : r),
            isLoading: false
          }));
        } catch (error) {
          set({ error: error instanceof Error ? error.message : 'Failed to mark reminder as read', isLoading: false });
        }
      },
      
      // Photos
      addPhoto: async (photoData) => {
        set({ isLoading: true, error: null });
        try {
          const { data, error } = await supabase
            .from('photos')
            .insert([{ ...photoData }])
            .select()
            .single();
          if (error) throw error;
          set(state => ({ photos: [...state.photos, data], isLoading: false }));
          return data;
        } catch (error) {
          set({ error: error instanceof Error ? error.message : 'Failed to add photo', isLoading: false });
          throw error;
        }
      },
      updatePhoto: async (photoId, updates) => {
        set({ isLoading: true, error: null });
        try {
          const { data, error } = await supabase
            .from('photos')
            .update(updates)
            .eq('id', photoId)
            .select()
            .single();
          if (error) throw error;
          set(state => ({
            photos: state.photos.map(p => p.id === photoId ? data : p),
            isLoading: false
          }));
        } catch (error) {
          set({ error: error instanceof Error ? error.message : 'Failed to update photo', isLoading: false });
        }
      },
      deletePhoto: async (photoId) => {
        set({ isLoading: true, error: null });
        try {
          const { error } = await supabase
            .from('photos')
            .delete()
            .eq('id', photoId);
          if (error) throw error;
          set(state => ({
            photos: state.photos.filter(photo => photo.id !== photoId),
            isLoading: false
          }));
        } catch (error) {
          set({ error: error instanceof Error ? error.message : 'Failed to delete photo', isLoading: false });
        }
      },
      likePhoto: async (photoId) => {
        set({ isLoading: true, error: null });
        try {
          // Increment likes atomically
          const { data, error } = await supabase.rpc('increment_photo_likes', { photo_id: photoId });
          if (error) throw error;
          // Refetch photo
          const { data: updated, error: fetchErr } = await supabase
            .from('photos')
            .select('*')
            .eq('id', photoId)
            .single();
          if (fetchErr) throw fetchErr;
          set(state => ({
            photos: state.photos.map(p => p.id === photoId ? updated : p),
            isLoading: false
          }));
        } catch (error) {
          set({ error: error instanceof Error ? error.message : 'Failed to like photo', isLoading: false });
        }
      },
      
      // Payments
      recordPayment: async (paymentData) => {
        set({ isLoading: true, error: null });
        try {
          const { data, error } = await supabase
            .from('payments')
            .insert([{ ...paymentData }])
            .select()
            .single();
          if (error) throw error;
          set(state => ({ payments: [...state.payments, data], isLoading: false }));
          // Optionally update session payment status
          if (paymentData.sessionId) {
            await supabase
              .from('sessions')
              .update({ paymentStatus: paymentData.status === 'completed' ? 'paid' : paymentData.status })
              .eq('id', paymentData.sessionId);
          }
          return data;
        } catch (error) {
          set({ error: error instanceof Error ? error.message : 'Failed to record payment', isLoading: false });
          throw error;
        }
      },
      updatePaymentStatus: async (paymentId, status) => {
        set({ isLoading: true, error: null });
        try {
          const { data, error } = await supabase
            .from('payments')
            .update({ status })
            .eq('id', paymentId)
            .select()
            .single();
          if (error) throw error;
          set(state => ({
            payments: state.payments.map(p => p.id === paymentId ? data : p),
            isLoading: false
          }));
          // Optionally update session payment status
          if (data.sessionId) {
            await supabase
              .from('sessions')
              .update({ paymentStatus: status === 'completed' ? 'paid' : status })
              .eq('id', data.sessionId);
          }
        } catch (error) {
          set({ error: error instanceof Error ? error.message : 'Failed to update payment status', isLoading: false });
        }
      },
      fetchPayments: async () => {
        set({ isLoading: true, error: null });
        try {
          const user = useAuthStore.getState().user;
          if (!user) throw new Error('User not authenticated');
          const { data, error } = await supabase
            .from('payments')
            .select('*')
            .eq('trainerId', user.id);
          if (error) throw error;
          set({ payments: data || [], isLoading: false });
        } catch (error) {
          set({ error: error instanceof Error ? error.message : 'Failed to fetch payments', isLoading: false });
        }
      },
      
      // Payouts
      requestPayout: async (amount, paymentMethodId) => {
        set({ isLoading: true, error: null });
        try {
          // Insert payout request into Supabase
          const user = useAuthStore.getState().user;
          if (!user) throw new Error('User not authenticated');
          const { data, error } = await supabase
            .from('payouts')
            .insert([{ trainerId: user.id, amount, paymentMethodId, status: 'processing', requestDate: new Date().toISOString() }])
            .select()
            .single();
          if (error) throw error;
          set(state => ({ payouts: [...state.payouts, data], isLoading: false }));
          return data;
        } catch (error) {
          set({ error: error instanceof Error ? error.message : 'Failed to request payout', isLoading: false });
          throw error;
        }
      },
      fetchPayouts: async () => {
        set({ isLoading: true, error: null });
        try {
          const user = useAuthStore.getState().user;
          if (!user) throw new Error('User not authenticated');
          const { data, error } = await supabase
            .from('payouts')
            .select('*')
            .eq('trainerId', user.id);
          if (error) throw error;
          set({ payouts: data || [], isLoading: false });
        } catch (error) {
          set({ error: error instanceof Error ? error.message : 'Failed to fetch payouts', isLoading: false });
        }
      },
      
      // Revenue
      fetchRevenue: async (period = 'all') => {
        set({ isLoading: true, error: null });
        try {
          const user = useAuthStore.getState().user;
          if (!user) throw new Error('User not authenticated');
          // Fetch revenue summary from Supabase (assume a view or function exists)
          const { data, error } = await supabase
            .from('trainer_revenue_summary')
            .select('*')
            .eq('trainerId', user.id)
            .single();
          if (error) throw error;
          set({ revenue: data || {}, isLoading: false });
        } catch (error) {
          set({ error: error instanceof Error ? error.message : 'Failed to fetch revenue data', isLoading: false });
        }
      },
      generateRevenueForUser: async (userId) => {
        // Optionally trigger a Supabase function or refresh
        await get().fetchRevenue('all');
      },
      
      // Real-time listeners for trainer-side data
      subscribeToRealtime: () => {
        const user = useAuthStore.getState().user;
        if (!user) return () => {};
        // Sessions
        const sessionSub = supabase
          .channel('sessions')
          .on('postgres_changes', { event: '*', schema: 'public', table: 'sessions', filter: `trainerId=eq.${user.id}` }, (payload) => {
            get().fetchSessions();
          })
          .subscribe();
        // Payments
        const paymentSub = supabase
          .channel('payments')
          .on('postgres_changes', { event: '*', schema: 'public', table: 'payments', filter: `trainerId=eq.${user.id}` }, (payload) => {
            get().fetchPayments();
          })
          .subscribe();
        // Reminders
        const reminderSub = supabase
          .channel('reminders')
          .on('postgres_changes', { event: '*', schema: 'public', table: 'reminders', filter: `trainerId=eq.${user.id}` }, (payload) => {
            get().fetchReminders();
          })
          .subscribe();
        // Photos
        const photoSub = supabase
          .channel('photos')
          .on('postgres_changes', { event: '*', schema: 'public', table: 'photos', filter: `trainerId=eq.${user.id}` }, (payload) => {
            get().fetchPhotos();
          })
          .subscribe();
        // Cleanup
        return () => {
          supabase.removeChannel(sessionSub);
          supabase.removeChannel(paymentSub);
          supabase.removeChannel(reminderSub);
          supabase.removeChannel(photoSub);
        };
      },
      
      // Helper fetchers for real-time
      fetchSessions: async () => {
        const user = useAuthStore.getState().user;
        if (!user) return;
        const { data, error } = await supabase
          .from('sessions')
          .select('*')
          .eq('trainerId', user.id);
        if (!error) set({ sessions: data || [] });
      },
      fetchReminders: async () => {
        const user = useAuthStore.getState().user;
        if (!user) return;
        const { data, error } = await supabase
          .from('reminders')
          .select('*')
          .eq('trainerId', user.id);
        if (!error) set({ reminders: data || [] });
      },
      fetchPhotos: async () => {
        const user = useAuthStore.getState().user;
        if (!user) return;
        const { data, error } = await supabase
          .from('photos')
          .select('*')
          .eq('trainerId', user.id);
        if (!error) set({ photos: data || [] });
      },
    }),
    {
      name: 'trainer-storage',
      storage: createJSONStorage(() => AsyncStorage),
    }
  )
);