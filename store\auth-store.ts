import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { UserRole } from '@/types';
import { Alert } from 'react-native';
import { trpcClient } from '@/lib/trpc';
import { supabase } from '@/lib/supabase';

interface User {
  id: string;
  email: string;
  name: string;
  role: UserRole;
  profileImage?: string;
  bio?: string;
  specialties?: string[];
  certifications?: string[];
  experience?: string;
  rating?: number;
  reviewCount?: number;
  hourlyRate?: number;
  rateType?: 'hourly' | 'custom';
  customRates?: {
    id: string;
    title: string;
    amount: number;
  }[];
  location?: {
    latitude: number;
    longitude: number;
    address: string;
  };
  socialLinks?: {
    instagram?: string;
    twitter?: string;
    facebook?: string;
    linkedin?: string;
    website?: string;
  };
  availability?: {
    days: string[];
    hours: {
      start: string;
      end: string;
    };
  };
  isVerified?: boolean;
}

interface AuthState {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
  registeredUsers: User[]; // Store registered users
  userPasswords: Record<string, string>; // Store passwords (for demo only)
  resetTokens: Record<string, { token: string, expiry: number }>; // Store reset tokens
  
  // Actions
  login: (email: string, password: string, role: UserRole) => Promise<void>;
  register: (name: string, email: string, password: string, role: UserRole) => Promise<void>;
  logout: () => void;
  updateProfile: (userData: Partial<User>) => Promise<void>;
  clearError: () => void;
  getTrainers: () => User[];
  getAllUsers: () => User[];
  debugUsers: () => void;
  requestPasswordReset: (email: string) => Promise<void>;
  verifyResetToken: (email: string, token: string) => Promise<boolean>;
  resetPassword: (email: string, token: string, newPassword: string) => Promise<void>;
}

export const useAuthStore = create<AuthState>()(
  persist(
    (set, get) => ({
      user: null,
      isAuthenticated: false,
      isLoading: false,
      error: null,
      registeredUsers: [],
      userPasswords: {}, // Store passwords in the persisted state
      resetTokens: {},
      
      clearError: () => {
        set({ error: null });
      },
      
      login: async (email, password, role) => {
        set({ isLoading: true, error: null });
        try {
          // Supabase sign in
          const { data, error: supaError } = await supabase.auth.signInWithPassword({
            email,
            password
          });
          if (supaError) throw new Error(supaError.message);
          if (!data.user) throw new Error('No user found');

          // Optionally fetch user profile from Supabase (if you have a user table)
          // const { data: profile } = await supabase.from('profiles').select('*').eq('id', data.user.id).single();

          set({
            user: {
              id: data.user.id,
              email: data.user.email || '',
              name: data.user.user_metadata?.name || '',
              role: data.user.user_metadata?.role || role,
              // ...add more fields as needed
            },
            isAuthenticated: true,
            isLoading: false,
            error: null
          });
          return;
        } catch (error) {
          set({ 
            error: error instanceof Error ? error.message : 'Login failed', 
            isLoading: false,
            isAuthenticated: false
          });
        }
      },
      
      register: async (name, email, password, role) => {
        set({ isLoading: true, error: null });
        try {
          // Supabase sign up
          const { data, error: supaError } = await supabase.auth.signUp({
            email,
            password,
            options: {
              data: { name, role }
            }
          });
          if (supaError) throw new Error(supaError.message);
          if (!data.user) throw new Error('Registration failed');

          set({
            user: {
              id: data.user.id,
              email: data.user.email || '',
              name: data.user.user_metadata?.name || name,
              role: data.user.user_metadata?.role || role,
              // ...add more fields as needed
            },
            isAuthenticated: true,
            isLoading: false,
            error: null
          });
          return;
        } catch (error) {
          set({ 
            error: error instanceof Error ? error.message : 'Registration failed', 
            isLoading: false,
            isAuthenticated: false
          });
        }
      },
      
      logout: () => {
        // Clear all auth state
        set({
          user: null,
          isAuthenticated: false,
          error: null,
          isLoading: false
        });
        console.log("Logout successful");
      },
      
      updateProfile: async (userData) => {
        set({ isLoading: true, error: null });
        try {
          // Simulate API call
          await new Promise(resolve => setTimeout(resolve, 1000));
          
          const currentUser = get().user;
          if (!currentUser) {
            throw new Error("No user logged in");
          }
          
          // Update user data
          const updatedUser = { ...currentUser, ...userData };
          
          // Update user in registered users list
          const updatedUsers = get().registeredUsers.map(user => 
            user.id === currentUser.id ? updatedUser : user
          );
          
          set({
            user: updatedUser,
            registeredUsers: updatedUsers,
            isLoading: false,
            error: null
          });
          
          console.log("Profile updated successfully for:", updatedUser.email);
        } catch (error) {
          console.log("Profile update error:", error);
          set({ 
            error: error instanceof Error ? error.message : "Profile update failed", 
            isLoading: false 
          });
        }
      },

      // Function to get all trainers
      getTrainers: () => {
        // Return all users with role 'trainer'
        const trainers = get().registeredUsers.filter(user => user.role === 'trainer');
        console.log(`Found ${trainers.length} registered trainers`);
        return trainers;
      },
      
      // Function to get all users (for debugging)
      getAllUsers: () => {
        return get().registeredUsers;
      },
      
      // Debug function to show all users
      debugUsers: () => {
        const users = get().registeredUsers;
        const passwords = get().userPasswords;
        console.log("All registered users:", users.map(u => ({ id: u.id, email: u.email, role: u.role })));
        console.log("Stored passwords:", Object.keys(passwords).length);
        
        // Show alert with user count
        Alert.alert(
          "Debug Info",
          `Total registered users: ${users.length}
Trainers: ${users.filter(u => u.role === 'trainer').length}
Clients: ${users.filter(u => u.role === 'client').length}
Passwords stored: ${Object.keys(passwords).length}
Emails with passwords: ${Object.keys(passwords).join(', ')}`,
          [{ text: "OK" }]
        );
      },
      
      // Request password reset
      requestPasswordReset: async (email) => {
        set({ isLoading: true, error: null });
        try {
          const { error: supaError } = await supabase.auth.resetPasswordForEmail(email);
          if (supaError) throw new Error(supaError.message);
          set({ isLoading: false, error: null });
          Alert.alert(
            'Password Reset Email Sent',
            'Check your email for a password reset link.',
            [{ text: 'OK' }]
          );
        } catch (error) {
          set({ 
            error: error instanceof Error ? error.message : 'Password reset request failed', 
            isLoading: false 
          });
        }
      },
      
      // Verify reset token
      verifyResetToken: async (email, token) => {
        const lowerEmail = email.toLowerCase();
        const resetData = get().resetTokens[lowerEmail];
        
        if (!resetData) {
          return false;
        }
        
        // Check if token matches and is not expired
        if (resetData.token === token && resetData.expiry > Date.now()) {
          return true;
        }
        
        return false;
      },
      
      // Reset password
      resetPassword: async (email, token, newPassword) => {
        set({ isLoading: true, error: null });
        try {
          // Supabase handles password reset via the link sent to email, not by token in-app
          // You may want to direct users to the link in their email
          set({ isLoading: false, error: null });
          Alert.alert(
            'Password Reset',
            'Please use the link sent to your email to reset your password.',
            [{ text: 'OK' }]
          );
        } catch (error) {
          set({ 
            error: error instanceof Error ? error.message : 'Password reset failed', 
            isLoading: false 
          });
        }
      }
    }),
    {
      name: 'auth-storage',
      storage: createJSONStorage(() => AsyncStorage),
      partialize: (state) => ({
        user: state.user,
        isAuthenticated: state.isAuthenticated,
        registeredUsers: state.registeredUsers,
        userPasswords: state.userPasswords,
        resetTokens: state.resetTokens
      }),
    }
  )
);