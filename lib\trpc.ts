import { createTRPCReact } from '@trpc/react-query';
import { httpBatchLink } from '@trpc/client';
import { AppRouter } from '@/backend/trpc/app-router';
import superjson from 'superjson';
import { Platform } from 'react-native';
import Constants from 'expo-constants';

export const trpc = createTRPCReact<AppRouter>();

// Create a transformer for data serialization
const transformer = superjson;

// Get the appropriate API URL based on platform and environment
const getApiUrl = () => {
  // Check if we're in production
  const isProduction = process.env.NODE_ENV === 'production';

  // For web platform
  if (Platform.OS === 'web') {
    if (isProduction) {
      // In production, use the deployed Vercel URL
      return 'https://trainfit-connections.vercel.app/api';
    }
    // In development, use relative URL
    return '/api/trpc';
  }

  // For native (Expo Go/React Native)
  if (isProduction) {
    // In production, mobile apps should connect to the deployed API
    return 'https://trainfit-connections.vercel.app/api';
  }

  // For development - use the development server's IP
  // In development, Expo provides the debugger host which we can use
  const debuggerHost = Constants.expoConfig?.hostUri?.split(':')[0];

  if (debuggerHost) {
    // Use the same host as the Expo development server but port 3000 for our API
    return `http://${debuggerHost}:3000/api/trpc`;
  }

  // Fallback to localhost (won't work in Expo Go but useful for development builds)
  return 'http://localhost:3000/api/trpc';
};

console.log('🔗 tRPC API URL:', getApiUrl());

// Custom fetch function with size limits and better error handling
const customFetch = async (input: RequestInfo | URL, init?: RequestInit) => {
  // Check if this is a POST request with a body
  if (init && init.method === 'POST' && init.body) {
    // Get the body size
    const bodySize = init.body instanceof Blob
      ? init.body.size
      : typeof init.body === 'string'
        ? new Blob([init.body]).size
        : 0;

    // If body size is too large (over 8MB), reject the request
    const maxSize = 8 * 1024 * 1024; // 8MB
    if (bodySize > maxSize) {
      console.error(`Request payload too large: ${bodySize} bytes (max: ${maxSize} bytes)`);
      throw new Error(`Payload too large: ${(bodySize / (1024 * 1024)).toFixed(2)}MB exceeds limit of 8MB`);
    }
  }

  try {
    // Use platform-specific fetch with timeout
    if (Platform.OS === 'web') {
      // For web, use standard fetch with timeout
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 30000);

      try {
        const response = await fetch(input, {
          ...init,
          signal: controller.signal,
        });

        // Check for 413 status code
        if (response.status === 413) {
          console.error('413 Payload Too Large error detected');
          throw new Error('Payload too large. Please reduce the size of your request.');
        }

        return response;
      } finally {
        clearTimeout(timeoutId);
      }
    } else {
      // For native, use fetch with AbortSignal.timeout if available
      const response = await fetch(input, {
        ...init,
        // Use AbortSignal.timeout if available, otherwise use a controller
        signal: AbortSignal.timeout ? AbortSignal.timeout(30000) : (() => {
          const controller = new AbortController();
          setTimeout(() => controller.abort(), 30000);
          return controller.signal;
        })(),
      });

      // Check for 413 status code
      if (response.status === 413) {
        console.error('413 Payload Too Large error detected');
        throw new Error('Payload too large. Please reduce the size of your request.');
      }

      return response;
    }
  } catch (error) {
    console.error('Fetch error:', error);

    // Enhance error message for payload size issues
    if (error instanceof Error &&
        (error.message.includes('payload') ||
         error.message.includes('size') ||
         error.message.includes('large'))) {
      throw new Error(`Request payload too large: ${error.message}`);
    }

    throw error;
  }
};

// Create a client with better error handling and retry logic
export const trpcClient = trpc.createClient({
  links: [
    httpBatchLink({
      url: getApiUrl(),
      transformer,
      // Add fetch options for better timeout handling
      fetch: customFetch,
      // Add headers
      headers: () => {
        return {
          'Content-Type': 'application/json',
          'X-Client-Platform': Platform.OS,
        };
      },
    }),
  ],
});