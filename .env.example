# Server Configuration
PORT=3000

# Supabase Configuration
SUPABASE_URL=https://iuwlgzguabqymurvcqub.supabase.co
SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Iml1d2xnemd1YWJxeW11cnZjcXViIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDgzNzgzNzAsImV4cCI6MjA2Mzk1NDM3MH0.rbu4bgO23kZDjwNgBPCO5ilR3IA4XJ_5d2tOWJ6PVss

# API Configuration for Expo Go
# When running with Expo Go, replace localhost with your computer's IP address
# You can find your IP by running: ipconfig (Windows) or ifconfig (Mac/Linux)
# Example: API_URL=http://*************:3000/api/trpc
API_URL=http://localhost:3000/api/trpc
