import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Trainer, Session, WorkoutPlan, MealPlan, Reminder, Payment, ReminderType, Certification, Availability, Client, CustomRate } from '@/types';
import { useAuthStore } from './auth-store';
import { useTrainerStore } from './trainer-store';
import { Alert } from 'react-native';
import { supabase } from '@/lib/supabase';

interface ClientState {
  nearbyTrainers: Trainer[];
  favoriteTrainers: string[];
  sessions: Session[];
  workoutPlans: WorkoutPlan[];
  mealPlans: MealPlan[];
  reminders: Reminder[];
  payments: Payment[];
  isLoading: boolean;
  error: string | null;
  lastRefreshed: number;
  
  // Trainer discovery
  searchTrainers: (query: string, filters?: any) => void;
  fetchNearbyTrainers: (latitude: number, longitude: number) => Promise<void>;
  toggleFavoriteTrainer: (trainerId: string) => void;
  
  // Session management
  bookSession: (session: Omit<Session, 'id'>) => Promise<Session>;
  cancelSession: (sessionId: string) => Promise<void>;
  
  // Payment management
  makePayment: (sessionId: string, amount: number, method: string) => Promise<void>;
  
  // Workout plans
  getWorkoutPlans: () => Promise<WorkoutPlan[]>;
  
  // Meal plans
  getMealPlans: () => Promise<MealPlan[]>;
  
  // Reminders
  getReminders: () => Promise<Reminder[]>;
  markReminderAsRead: (reminderId: string) => Promise<void>;
  deleteReminder: (reminderId: string) => Promise<void>;
  
  // Debug
  debugTrainers: () => void;
}

// Create a session for today
const today = new Date();
const todayString = today.toISOString();

// Create the client store
export const useClientStore = create<ClientState>()(
  persist(
    (set, get) => ({
      nearbyTrainers: [],
      favoriteTrainers: ['t1', 't3'],
      sessions: [],
      workoutPlans: [],
      mealPlans: [],
      reminders: [],
      payments: [],
      isLoading: false,
      error: null,
      lastRefreshed: Date.now(),
      
      searchTrainers: (query, filters) => {
        set({ isLoading: true });
        // Get all trainers from auth store
        const registeredTrainersRaw = useAuthStore.getState().getTrainers();
        // Map to Trainer type
        const registeredTrainers: Trainer[] = registeredTrainersRaw.map((t: any) => ({
          id: t.id,
          name: t.name,
          email: t.email,
          role: 'trainer',
          profileImage: t.profileImage || '',
          bio: t.bio || '',
          specialties: t.specialties || [],
          certifications: t.certifications || [],
          experience: typeof t.experience === 'number' ? t.experience : 0,
          rating: typeof t.rating === 'number' ? t.rating : 0,
          reviewCount: typeof t.reviewCount === 'number' ? t.reviewCount : 0,
          pricing: t.pricing || { oneOnOne: 50, group: 25, virtual: 40 },
          location: t.location || { latitude: 0, longitude: 0, address: '' },
          socialLinks: t.socialLinks || {},
          availability: t.availability || [],
          isVerified: !!t.isVerified,
          hourlyRate: typeof t.hourlyRate === 'number' ? t.hourlyRate : 50,
          rateType: t.rateType || 'hourly',
          customRates: t.customRates || [],
          clients: t.clients || [],
        }));
        // Combine with mock trainers if needed
        const allTrainers = [...registeredTrainers];
        // Remove duplicates (in case mock trainers overlap with registered ones)
        const uniqueTrainers = Array.from(new Map(allTrainers.map(trainer => [trainer.id, trainer])).values());
        // Simulate API call
        setTimeout(() => {
          // Filter trainers based on query
          const filteredTrainers = uniqueTrainers.filter(trainer => 
            trainer.name.toLowerCase().includes(query.toLowerCase()) ||
            trainer.specialties?.some(s => s.toLowerCase().includes(query.toLowerCase())) ||
            trainer.location?.address.toLowerCase().includes(query.toLowerCase())
          );
          set({ 
            nearbyTrainers: filteredTrainers,
            isLoading: false,
            lastRefreshed: Date.now()
          });
        }, 500);
      },
      
      fetchNearbyTrainers: async (latitude, longitude) => {
        set({ isLoading: true, error: null });
        try {
          // Fetch trainers from Supabase
          const { data: trainers, error } = await supabase
            .from('profiles')
            .select('*')
            .eq('role', 'trainer');
          if (error) throw error;
          set({ 
            nearbyTrainers: trainers || [],
            isLoading: false,
            lastRefreshed: Date.now()
          });
          return Promise.resolve();
        } catch (error) {
          set({ 
            error: error instanceof Error ? error.message : 'Failed to fetch nearby trainers', 
            isLoading: false 
          });
          return Promise.reject(error);
        }
      },
      
      toggleFavoriteTrainer: (trainerId) => {
        set(state => {
          const isFavorite = state.favoriteTrainers.includes(trainerId);
          
          // Get current user
          const currentUser = useAuthStore.getState().user;
          if (!currentUser) {
            console.error("Cannot toggle favorite: User not logged in");
            return state;
          }
          
          // Get trainer store
          const trainerStore = useTrainerStore.getState();
          
          if (isFavorite) {
            // Remove from favorites
            return {
              favoriteTrainers: state.favoriteTrainers.filter(id => id !== trainerId)
            };
          } else {
            // Add to favorites
            
            // Also add client to trainer's client list if not already there
            const trainer = state.nearbyTrainers.find(t => t.id === trainerId);
            if (trainer) {
              // Check if client is already in trainer's client list
              const isClientAlreadyAdded = trainer.clients?.includes(currentUser.id);
              
              if (!isClientAlreadyAdded) {
                console.log(`Adding client ${currentUser.id} to trainer ${trainerId}'s client list`);
                
                // Create a client object from the current user
                const clientToAdd = {
                  id: currentUser.id,
                  name: currentUser.name,
                  email: currentUser.email,
                  role: 'client' as const,
                  profileImage: currentUser.profileImage,
                  location: currentUser.location,
                  sessionCount: 0,
                  bio: currentUser.bio || "New client"
                };
                
                // Add client to trainer's client list
                trainerStore.addClient(clientToAdd);
                
                // Create a notification for the trainer
                const notification: Omit<Reminder, 'id'> = {
                  trainerId: trainerId,
                  clientId: currentUser.id,
                  title: 'New Potential Client',
                  message: `${currentUser.name} has added you to their favorites! They might be interested in booking a session with you soon.`,
                  date: new Date().toISOString(),
                  time: new Date().toLocaleTimeString('en-US', { hour: '2-digit', minute: '2-digit' }),
                  isRead: false,
                  type: 'other'
                };
                
                // Add notification to trainer's reminders
                trainerStore.createReminder(notification);
              }
            }
            
            return {
              favoriteTrainers: [...state.favoriteTrainers, trainerId]
            };
          }
        });
      },
      
      bookSession: async (sessionData) => {
        set({ isLoading: true, error: null });
        try {
          // Insert session into Supabase
          const { data, error } = await supabase
            .from('sessions')
            .insert([{ ...sessionData, status: 'pending' }])
            .select()
            .single();
          if (error) throw error;
          set(state => ({
            sessions: [...state.sessions, data],
            isLoading: false
          }));
          return data;
        } catch (error) {
          set({ 
            error: error instanceof Error ? error.message : 'Failed to book session', 
            isLoading: false 
          });
          throw error;
        }
      },
      
      cancelSession: async (sessionId) => {
        set({ isLoading: true, error: null });
        try {
          // Update session status in Supabase
          const { error } = await supabase
            .from('sessions')
            .update({ status: 'cancelled' })
            .eq('id', sessionId);
          if (error) throw error;
          set(state => ({
            sessions: state.sessions.map(session => 
              session.id === sessionId ? { ...session, status: 'cancelled' } : session
            ),
            isLoading: false
          }));
        } catch (error) {
          set({ 
            error: error instanceof Error ? error.message : 'Failed to cancel session', 
            isLoading: false 
          });
        }
      },
      
      makePayment: async (sessionId, amount, method) => {
        set({ isLoading: true, error: null });
        try {
          // Insert payment into Supabase
          const { data, error } = await supabase
            .from('payments')
            .insert([{ sessionId, amount, method, status: 'completed', date: new Date().toISOString() }])
            .select()
            .single();
          if (error) throw error;
          set(state => ({
            payments: [...state.payments, data],
            isLoading: false
          }));
        } catch (error) {
          set({ 
            error: error instanceof Error ? error.message : 'Failed to process payment', 
            isLoading: false 
          });
          throw error;
        }
      },
      
      getWorkoutPlans: async () => {
        set({ isLoading: true });
        try {
          const userId = useAuthStore.getState().user?.id;
          if (!userId) throw new Error('User not authenticated');
          const { data, error } = await supabase
            .from('workout_plans')
            .select('*')
            .eq('clientId', userId);
          if (error) throw error;
          set({ workoutPlans: data || [], isLoading: false });
          return data || [];
        } catch (error) {
          set({ error: error instanceof Error ? error.message : 'Failed to fetch workout plans', isLoading: false });
          return [];
        }
      },
      
      getMealPlans: async () => {
        set({ isLoading: true });
        try {
          const userId = useAuthStore.getState().user?.id;
          if (!userId) throw new Error('User not authenticated');
          const { data, error } = await supabase
            .from('meal_plans')
            .select('*')
            .eq('clientId', userId);
          if (error) throw error;
          set({ mealPlans: data || [], isLoading: false });
          return data || [];
        } catch (error) {
          set({ error: error instanceof Error ? error.message : 'Failed to fetch meal plans', isLoading: false });
          return [];
        }
      },
      
      getReminders: async () => {
        set({ isLoading: true });
        try {
          const userId = useAuthStore.getState().user?.id;
          if (!userId) throw new Error('User not authenticated');
          const { data, error } = await supabase
            .from('reminders')
            .select('*')
            .eq('clientId', userId);
          if (error) throw error;
          set({ reminders: data || [], isLoading: false });
          return data || [];
        } catch (error) {
          set({ error: error instanceof Error ? error.message : 'Failed to fetch reminders', isLoading: false });
          return [];
        }
      },
      
      markReminderAsRead: async (reminderId) => {
        try {
          await supabase
            .from('reminders')
            .update({ isRead: true })
            .eq('id', reminderId);
          set(state => ({
            reminders: state.reminders.map(reminder => 
              reminder.id === reminderId ? { ...reminder, isRead: true } : reminder
            )
          }));
        } catch (error) {
          set({ error: error instanceof Error ? error.message : 'Failed to mark reminder as read' });
        }
      },
      
      deleteReminder: async (reminderId) => {
        try {
          await supabase
            .from('reminders')
            .delete()
            .eq('id', reminderId);
          set(state => ({
            reminders: state.reminders.filter(reminder => reminder.id !== reminderId)
          }));
        } catch (error) {
          set({ error: error instanceof Error ? error.message : 'Failed to delete reminder' });
        }
      },
      
      // Debug function to show all trainers
      debugTrainers: () => {
        const registeredTrainers = useAuthStore.getState().getTrainers();
        const nearbyTrainers = get().nearbyTrainers;
        
        console.log("Registered trainers:", registeredTrainers.map(t => ({ id: t.id, email: t.email, name: t.name })));
        console.log("Nearby trainers:", nearbyTrainers.map(t => ({ id: t.id, email: t.email, name: t.name })));
        
        // Show alert with trainer count
        Alert.alert(
          "Debug Trainer Info",
          `Registered trainers: ${registeredTrainers.length}
Nearby trainers: ${nearbyTrainers.length}
Last refreshed: ${new Date(get().lastRefreshed).toLocaleTimeString()}`,
          [
            { 
              text: "Refresh Now", 
              onPress: () => {
                const user = useAuthStore.getState().user;
                if (user?.location) {
                  get().fetchNearbyTrainers(user.location.latitude, user.location.longitude);
                  Alert.alert("Refreshed", "Trainer data has been refreshed");
                } else {
                  Alert.alert("Error", "User location not available");
                }
              } 
            },
            { text: "OK" }
          ]
        );
      }
    }),
    {
      name: 'client-storage',
      storage: createJSONStorage(() => AsyncStorage),
    }
  )
);