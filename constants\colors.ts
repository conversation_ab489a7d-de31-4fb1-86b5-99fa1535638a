const Colors = {
  primary: '#059669', // Emerald 600
  secondary: '#7C3AED', // Violet 600
  accent: '#0EA5E9', // Sky 500
  
  // Status colors at root level for backward compatibility
  success: '#10B981', // Emerald 500
  error: '#EF4444',   // Red 500
  warning: '#F59E0B',  // Amber 500
  info: '#3B82F6',    // Blue 500
  
  background: {
    light: '#F9FAFB', // Gray 50
    dark: '#1F2937',  // Gray 800
    darker: '#111827', // Gray 900
    card: '#374151',  // Gray 700
    primary: '#059669', // Emerald 600
    input: '#4B5563',  // Gray 600
  },
  text: {
    primary: '#F9FAFB',   // Gray 50
    secondary: '#D1D5DB', // Gray 300
    tertiary: '#9CA3AF',  // Gray 400
    inverse: '#111827',   // Gray 900
  },
  border: {
    light: '#4B5563',  // Gray 600
    medium: '#374151', // Gray 700
    dark: '#1F2937',   // Gray 800
  },
  shadow: '#000000',
  state: {
    active: '#059669',   // Emerald 600
    inactive: '#6B7280', // Gray 500
    disabled: '#4B5563', // Gray 600
  },
  status: {
    success: '#10B981', // Emerald 500
    error: '#EF4444',   // Red 500
    warning: '#F59E0B',  // Amber 500
    info: '#3B82F6',    // Blue 500
  }
};

export default Colors;