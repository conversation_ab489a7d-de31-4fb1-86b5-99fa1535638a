// Polyfills for Node.js modules in React Native
import 'react-native-url-polyfill/auto';
import { Buff<PERSON> } from 'buffer';

// Make Buffer available globally
global.Buffer = Buffer;

// Polyfill process.env if needed
if (typeof global.process === 'undefined') {
  global.process = {
    env: {},
    nextTick: (fn, ...args) => setTimeout(() => fn(...args), 0),
    version: '',
    platform: 'react-native'
  };
}

// Polyfill global if needed
if (typeof global.global === 'undefined') {
  global.global = global;
}

// Polyfill EventEmitter for events module
if (typeof global.EventEmitter === 'undefined') {
  const { EventEmitter } = require('events');
  global.EventEmitter = EventEmitter;
}

// Export for explicit imports
export { Buffer };
