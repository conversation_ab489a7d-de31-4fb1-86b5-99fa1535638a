# Expo Go Setup Guide for TrainFit Connections

This guide will help you set up the TrainFit Connections app to work properly with Expo Go and Supabase.

## Prerequisites

1. **Node.js** (v18 or later)
2. **Expo CLI** (`npm install -g @expo/cli`)
3. **Expo Go app** installed on your mobile device
4. **Network access** - Your computer and mobile device must be on the same network

## Setup Instructions

### 1. Install Dependencies

```bash
npm install
```

### 2. Start the Backend Server

The app requires a backend server to handle tRPC API calls. Start it with:

```bash
npm run server
```

This will start the Hono server on port 3000. You should see:
```
✅ Server is running on http://localhost:3000
📱 API endpoints available at:
   - Health check: http://localhost:3000/api/health
   - tRPC: http://localhost:3000/api/trpc
   - Example: http://localhost:3000/api/trpc/example.hi
```

### 3. Start the Expo Development Server

In a new terminal window, start the Expo development server:

```bash
npm start
```

Or use the combined command to start both server and Expo:

```bash
npm run dev
```

### 4. Connect with Expo Go

1. Open the Expo Go app on your mobile device
2. Scan the QR code displayed in your terminal or browser
3. The app should load and connect to both the Expo server and your backend API

## Configuration Details

### Automatic IP Detection

The app is configured to automatically detect your computer's IP address when running in Expo Go:

- **Web**: Uses relative URLs (`/api/trpc`)
- **Expo Go**: Automatically detects the development server IP and uses it for API calls
- **Development Builds**: Falls back to localhost

### Supabase Configuration

Supabase is configured with:
- **URL**: `https://iuwlgzguabqymurvcqub.supabase.co`
- **Project**: `trainfit-connections`
- **Status**: Active and healthy ✅

The configuration is embedded in `app.json` and automatically loaded by the app.

## Troubleshooting

### Backend Server Issues

If the backend server fails to start:

1. Check if port 3000 is available:
   ```bash
   netstat -an | findstr :3000  # Windows
   lsof -i :3000                # Mac/Linux
   ```

2. Try a different port:
   ```bash
   PORT=3001 npm run server
   ```

### Expo Go Connection Issues

If Expo Go can't connect to the API:

1. **Check Network**: Ensure your computer and phone are on the same WiFi network
2. **Firewall**: Make sure your firewall allows connections on port 3000
3. **Manual IP Configuration**: If automatic detection fails, you can manually set the IP in `.env`:
   ```
   API_URL=http://YOUR_COMPUTER_IP:3000/api/trpc
   ```

### Finding Your Computer's IP Address

**Windows:**
```bash
ipconfig
```
Look for "IPv4 Address" under your active network adapter.

**Mac/Linux:**
```bash
ifconfig
```
Look for "inet" under your active network interface (usually en0 or wlan0).

### Supabase Connection Issues

If Supabase authentication or data fetching fails:

1. Check the Supabase project status in the dashboard
2. Verify the URL and anon key in `app.json`
3. Check network connectivity to `https://iuwlgzguabqymurvcqub.supabase.co`

## Testing the Setup

### 1. Test Backend Health

Visit `http://localhost:3000/api/health` in your browser. You should see:
```json
{
  "status": "ok",
  "timestamp": "2024-01-XX..."
}
```

### 2. Test tRPC Endpoint

Visit `http://localhost:3000/api/trpc/example.hi` to test the tRPC setup.

### 3. Test in Expo Go

1. Open the app in Expo Go
2. Try logging in or registering a new account
3. Check that Supabase authentication works
4. Verify that data loading and API calls function properly

## Available Scripts

- `npm start` - Start Expo development server with tunnel
- `npm run server` - Start the backend server only
- `npm run dev` - Start both backend server and Expo (recommended)
- `npm run start-web` - Start Expo for web development
- `npm run dev-web` - Start both backend and web development

## Production Deployment

For production deployment:

1. **Backend**: Deploy the Hono server to a cloud provider (Vercel, Railway, etc.)
2. **Frontend**: Build and deploy the Expo app using EAS Build
3. **Environment**: Update the API URLs to point to your production backend

## Support

If you encounter issues:

1. Check the console logs in both the terminal and Expo Go
2. Verify all dependencies are installed correctly
3. Ensure your network configuration allows the connections
4. Check the Supabase project dashboard for any service issues
