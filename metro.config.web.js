const { getDefaultConfig } = require('expo/metro-config');

/** @type {import('expo/metro-config').MetroConfig} */
const config = getDefaultConfig(__dirname);

// Web-specific configuration to exclude native-only modules
config.resolver.platforms = ['web', 'native'];

// Exclude native-only modules for web builds
config.resolver.alias = {
  ...config.resolver.alias,
  'react-native-maps': false,
  'react-native-camera': false,
  'react-native-location': false,
  // Keep polyfills for web
  'events': require.resolve('events'),
  'stream': require.resolve('readable-stream'),
  'util': require.resolve('util'),
  'buffer': require.resolve('buffer'),
  'crypto': require.resolve('react-native-crypto-js'),
  'http': require.resolve('stream-http'),
  'https': require.resolve('https-browserify'),
  'os': require.resolve('react-native-os'),
  'url': require.resolve('url'),
  'fs': false,
  'net': false,
  'tls': false,
  // Use React Native's built-in WebSocket instead of Node.js ws
  'ws': require.resolve('./websocket-polyfill.js'),
};

module.exports = config;
