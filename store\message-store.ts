import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Message, MessageThread, Reminder } from '@/types';
import { useAuthStore } from './auth-store';
import { supabase } from '@/lib/supabase';
import { useEffect } from 'react';

interface MessageState {
  threads: MessageThread[];
  messages: Message[];
  messageNotifications: <PERSON>mind<PERSON>[];
  isLoading: boolean;
  error: string | null;
  hasUnreadMessages: boolean;

  // Actions
  fetchThreads: () => Promise<void>;
  fetchMessages: (threadId?: string, recipientId?: string) => Promise<void>;
  sendMessage: (messageData: { content: string; recipientId: string; threadId?: string }) => Promise<void>;
  markThreadAsRead: (threadId: string) => void;
  markMessageNotificationAsRead: (notificationId: string) => void;
  deleteThread: (threadId: string) => void;
  subscribeToRealtime: () => () => void; // New action for subscribing to real-time updates
}

// Mock message notifications
const mockMessageNotifications: Reminder[] = [
  {
    id: 'mn1',
    trainerId: 't1',
    clientId: 'c1',
    title: 'New Message',
    message: "Emma Wilson: Thanks for the workout plan! I'll start tomorrow.",
    date: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
    time: new Date(Date.now() - 2 * 60 * 60 * 1000).toLocaleTimeString('en-US', { hour: '2-digit', minute: '2-digit' }),
    isRead: false,
    type: 'message',
    relatedId: 'thread1',
    senderId: 'c1',
    senderName: 'Emma Wilson',
    senderImage: 'https://images.unsplash.com/photo-1494790108377-be9c29b29330?q=80&w=1000',
  },
];

export const useMessageStore = create<MessageState>()(
  persist(
    (set, get) => ({
      threads: [],
      messages: [],
      messageNotifications: mockMessageNotifications,
      isLoading: false,
      error: null,
      hasUnreadMessages: false,

      fetchThreads: async () => {
        set({ isLoading: true, error: null });
        try {
          const { user } = useAuthStore.getState();
          if (!user) throw new Error('User not authenticated');

          // Fetch threads where user is a participant
          const { data: participantRows, error: partErr } = await supabase
            .from('thread_participants')
            .select('thread_id')
            .or(`user_id_text.eq.${user.id},user_id.eq.${user.id}`);
          if (partErr) throw partErr;
          const threadIds = participantRows?.map(row => row.thread_id) || [];

          if (threadIds.length === 0) {
            set({ threads: [], hasUnreadMessages: false, isLoading: false });
            return;
          }

          // Fetch threads
          const { data: threads, error: threadErr } = await supabase
            .from('message_threads')
            .select('*')
            .in('id', threadIds)
            .order('updated_at', { ascending: false });
          if (threadErr) throw threadErr;

          // Optionally, fetch unread count per thread
          // (You can expand this logic as needed)

          set({ threads: threads || [], hasUnreadMessages: false, isLoading: false });
        } catch (error) {
          set({
            error: error instanceof Error ? error.message : 'Failed to fetch message threads',
            isLoading: false
          });
        }
      },
      fetchMessages: async (threadId, recipientId) => {
        set({ isLoading: true, error: null });
        try {
          if (!threadId) throw new Error('No thread selected');
          const { data: messages, error: msgErr } = await supabase
            .from('messages')
            .select('*')
            .eq('thread_id', threadId)
            .order('sent_at', { ascending: true });
          if (msgErr) throw msgErr;
          set({ messages: messages || [], isLoading: false });
        } catch (error) {
          set({
            error: error instanceof Error ? error.message : 'Failed to fetch messages',
            isLoading: false
          });
        }
      },
      sendMessage: async ({ content, recipientId, threadId }) => {
        set({ isLoading: true, error: null });
        try {
          const { user } = useAuthStore.getState();
          if (!user) throw new Error('User not authenticated');

          let usedThreadId = threadId;
          // If no threadId, create a new thread and add participants
          if (!threadId) {
            const { data: thread, error: threadErr } = await supabase
              .from('message_threads')
              .insert({})
              .select()
              .single();
            if (threadErr) throw threadErr;
            usedThreadId = thread.id;
            // Add participants
            await supabase.from('thread_participants').insert([
              { thread_id: usedThreadId, user_id_text: user.id },
              { thread_id: usedThreadId, user_id_text: recipientId }
            ]);
          }

          // Insert message
          const { data: message, error: msgErr } = await supabase
            .from('messages')
            .insert({
              thread_id: usedThreadId,
              sender_id: user.id,
              content
            })
            .select()
            .single();
          if (msgErr) throw msgErr;

          // Update thread updated_at
          await supabase
            .from('message_threads')
            .update({ updated_at: new Date().toISOString() })
            .eq('id', usedThreadId);

          // Update local state
          set(state => ({
            messages: [...state.messages, message],
            isLoading: false
          }));

          // Optionally, update threads list
          get().fetchThreads();
        } catch (error) {
          set({
            error: error instanceof Error ? error.message : 'Failed to send message',
            isLoading: false,
          });
        }
      },

      markThreadAsRead: async (threadId) => {
        try {
          const { user } = useAuthStore.getState();
          if (!user) return;
          // Mark all messages in this thread as read for the current user (if you have a read status per user, otherwise skip)
          // For now, just update local state
          set(state => {
            // Mark all messages in this thread as read (if not sent by current user)
            const updatedMessages = state.messages.map(message =>
              message.threadId === threadId && message.senderId !== user.id
                ? { ...message, isRead: true }
                : message
            );
            // Update thread's unread count and last message read status
            const updatedThreads = state.threads.map(thread =>
              thread.id === threadId
                ? {
                    ...thread,
                    unreadCount: 0,
                    lastMessage: thread.lastMessage ? {
                      ...thread.lastMessage,
                      isRead: true
                    } : thread.lastMessage
                  }
                : thread
            );
            // Update hasUnreadMessages flag
            const hasUnread = updatedThreads.some(thread =>
              thread.id !== threadId && thread.lastMessage && !thread.lastMessage.isRead
            );
            return {
              messages: updatedMessages,
              threads: updatedThreads,
              hasUnreadMessages: hasUnread,
            };
          });
        } catch (error: any) {
          console.error('Error marking thread as read:', error);
        }
      },

      markMessageNotificationAsRead: (notificationId) => {
        set(state => ({
          messageNotifications: state.messageNotifications.map(notification =>
            notification.id === notificationId
              ? { ...notification, isRead: true }
              : notification
          ),
        }));
      },

      deleteThread: (threadId) => {
        set(state => ({
          threads: state.threads.filter(thread => thread.id !== threadId),
          // Also remove messages for this thread
          messages: state.messages.filter(message => {
            const thread = state.threads.find(t => t.id === threadId);
            if (!thread) return true;
            return !(
              thread.participants.includes(message.senderId) &&
              thread.participants.includes(message.recipientId)
            );
          }),
        }));
      },
      subscribeToRealtime: () => {
        const { user } = useAuthStore.getState();
        if (!user) return () => {};

        console.log('🔄 Setting up polling-based real-time messaging for user:', user.id);

        // Use polling for reliable real-time updates (no WebSocket dependencies)
        const pollInterval = setInterval(() => {
          try {
            // Refresh threads and current messages
            get().fetchThreads();

            // If we have a current thread, refresh its messages
            const currentMessages = get().messages;
            if (currentMessages.length > 0) {
              const threadId = currentMessages[0]?.threadId;
              if (threadId) {
                get().fetchMessages(threadId);
              }
            }
          } catch (error) {
            console.error('❌ Error during polling update:', error);
          }
        }, 3000); // Poll every 3 seconds

        console.log('✅ Polling-based real-time updates started (no WebSocket dependencies)');

        // Return cleanup function
        return () => {
          console.log('🛑 Stopping polling-based real-time updates');
          clearInterval(pollInterval);
        };
      },
    }),
    {
      name: 'message-storage',
      storage: createJSONStorage(() => AsyncStorage),
    }
  )
);

// React hook to auto-subscribe to real-time updates
export function useMessageRealtime() {
  useEffect(() => {
    const unsubscribe = useMessageStore.getState().subscribeToRealtime?.();
    return () => {
      if (unsubscribe) unsubscribe();
    };
  }, []);
}