import { <PERSON>o } from "hono";
import { cors } from "hono/cors";
import { trpcServer } from "@hono/trpc-server";
import { fetchRequest<PERSON>andler } from "@trpc/server/adapters/fetch";
import { appRouter } from "./trpc/app-router";
import { createContext } from "./trpc/trpc";

const app = new Hono();

// Enable CORS
app.use(
  "/*",
  cors({
    origin: "*",
    allowHeaders: ["Content-Type", "Authorization"],
    allowMethods: ["GET", "POST", "PUT", "DELETE", "OPTIONS"],
    maxAge: 86400,
  })
);

// Add request size limit middleware - IMPROVED ERROR HANDLING
app.use("*", async (c, next) => {
  const contentLength = c.req.header('content-length');

  // If content length is provided, check if it exceeds the limit (10MB)
  if (contentLength) {
    const size = parseInt(contentLength, 10);
    const maxSize = 10 * 1024 * 1024; // 10MB

    if (size > maxSize) {
      console.error(`Request payload too large: ${size} bytes (max: ${maxSize} bytes)`);
      return c.json(
        {
          error: "Payload too large",
          message: "The request payload exceeds the maximum allowed size of 10MB.",
          details: {
            requestSize: `${(size / (1024 * 1024)).toFixed(2)}MB`,
            maxAllowedSize: "10MB"
          }
        },
        413
      );
    }
  }

  try {
    await next();
  } catch (error) {
    // Check if the error is related to payload size
    if (error instanceof Error &&
        (error.message.includes('payload') ||
         error.message.includes('size') ||
         error.message.includes('large'))) {
      console.error('Payload size error:', error);
      return c.json(
        {
          error: "Payload too large",
          message: error.message || "The request payload is too large.",
          details: {
            maxAllowedSize: "10MB"
          }
        },
        413
      );
    }

    // Re-throw other errors
    throw error;
  }
});

// Add request timeout handling
app.use("*", async (c, next) => {
  try {
    // Set a timeout for the request
    const timeoutPromise = new Promise((_, reject) => {
      setTimeout(() => {
        reject(new Error("Request timeout"));
      }, 25000); // 25 second timeout
    });

    // Race the request against the timeout
    await Promise.race([next(), timeoutPromise]);
  } catch (error) {
    if (error instanceof Error && error.message === "Request timeout") {
      return c.json({ error: "Request timeout" }, 504);
    }
    throw error;
  }
});

// Mount tRPC router using trpcServer middleware (simplified)
app.use("/api/trpc/*", trpcServer({
  router: appRouter,
  createContext,
}));

// Add direct messaging endpoints as a workaround
app.post("/api/messages/send", async (c) => {
  try {
    const body = await c.req.json();
    console.log('Direct message send request:', body);

    // Extract the actual data from tRPC format or direct format
    const data = body["0"]?.json || body;

    if (!data.content || !data.recipientId) {
      return c.json({ error: "Missing required fields: content, recipientId" }, 400);
    }

    // Create mock message response
    const newMessage = {
      id: Math.random().toString(36).substring(2, 11),
      threadId: data.threadId || Math.random().toString(36).substring(2, 11),
      senderId: 't1',
      senderName: 'John Trainer',
      senderRole: 'trainer',
      senderImage: 'https://images.unsplash.com/photo-1517838277536-f5f99be501cd?q=80&w=1000',
      content: data.content,
      timestamp: new Date().toISOString(),
      read: false,
    };

    return c.json({
      success: true,
      message: newMessage,
    });
  } catch (error) {
    console.error('Error in direct message send:', error);
    return c.json({ error: "Failed to send message" }, 500);
  }
});

app.get("/api/messages/:threadId", async (c) => {
  try {
    const threadId = c.req.param('threadId');
    console.log('Direct message fetch request for thread:', threadId);

    // Return mock messages for the thread
    const mockMessages = [
      {
        id: '1',
        threadId: threadId,
        senderId: 't1',
        senderName: 'John Trainer',
        senderRole: 'trainer',
        senderImage: 'https://images.unsplash.com/photo-1517838277536-f5f99be501cd?q=80&w=1000',
        content: 'Hello! How can I help you today?',
        timestamp: new Date(Date.now() - 3600000).toISOString(),
        read: true,
      },
      {
        id: '2',
        threadId: threadId,
        senderId: 'c1',
        senderName: 'Client User',
        senderRole: 'client',
        senderImage: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?q=80&w=1000',
        content: 'Hi! I have some questions about my workout plan.',
        timestamp: new Date(Date.now() - 1800000).toISOString(),
        read: true,
      }
    ];

    return c.json({
      success: true,
      messages: mockMessages,
    });
  } catch (error) {
    console.error('Error in direct message fetch:', error);
    return c.json({ error: "Failed to fetch messages" }, 500);
  }
});

// Comprehensive health check endpoint
app.get("/api/health", async (c) => {
  const startTime = Date.now();

  try {
    // Test Supabase connection
    const { createClient } = await import('@supabase/supabase-js');
    const supabaseUrl = 'https://iuwlgzguabqymurvcqub.supabase.co';
    const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.rbu4bgO23kZDjwNgBPCO5ilR3IA4XJ_5d2tOWJ6PVss';
    const supabase = createClient(supabaseUrl, supabaseAnonKey);

    // Test database connection
    const { data: dbTest, error: dbError } = await supabase
      .from('message_threads')
      .select('count')
      .limit(1);

    const responseTime = Date.now() - startTime;

    return c.json({
      status: "ok",
      timestamp: new Date().toISOString(),
      message: "TrainFit Connections API is running",
      services: {
        api: {
          status: "healthy",
          responseTime: `${responseTime}ms`
        },
        database: {
          status: dbError ? "error" : "healthy",
          error: dbError?.message || null
        },
        supabase: {
          status: "connected",
          url: supabaseUrl
        },
        websockets: {
          status: "enabled",
          transport: "react-native-websocket"
        }
      },
      version: "1.0.0",
      environment: process.env.NODE_ENV || "development"
    });
  } catch (error) {
    const responseTime = Date.now() - startTime;
    return c.json({
      status: "error",
      timestamp: new Date().toISOString(),
      message: "Health check failed",
      error: error instanceof Error ? error.message : 'Unknown error',
      responseTime: `${responseTime}ms`
    }, 500);
  }
});

// WebSocket connection test endpoint
app.get("/api/websocket/test", async (c) => {
  try {
    const { createClient } = await import('@supabase/supabase-js');
    const supabaseUrl = 'https://iuwlgzguabqymurvcqub.supabase.co';
    const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.rbu4bgO23kZDjwNgBPCO5ilR3IA4XJ_5d2tOWJ6PVss';

    const supabase = createClient(supabaseUrl, supabaseAnonKey, {
      realtime: {
        transport: WebSocket,
        timeout: 20000,
        heartbeatIntervalMs: 30000,
      },
    });

    // Test real-time connection
    const channel = supabase.channel('test-channel');

    return new Promise((resolve) => {
      const timeout = setTimeout(() => {
        channel.unsubscribe();
        resolve(c.json({
          status: "timeout",
          message: "WebSocket connection test timed out",
          timestamp: new Date().toISOString()
        }));
      }, 10000);

      channel
        .on('subscribe', (status) => {
          clearTimeout(timeout);
          channel.unsubscribe();
          resolve(c.json({
            status: "success",
            message: "WebSocket connection successful",
            connectionStatus: status,
            timestamp: new Date().toISOString()
          }));
        })
        .on('error', (error) => {
          clearTimeout(timeout);
          channel.unsubscribe();
          resolve(c.json({
            status: "error",
            message: "WebSocket connection failed",
            error: error.message,
            timestamp: new Date().toISOString()
          }, 500));
        })
        .subscribe();
    });
  } catch (error) {
    return c.json({
      status: "error",
      message: "WebSocket test failed",
      error: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    }, 500);
  }
});

// Add a catch-all route for debugging
app.all("*", (c) => {
  const method = c.req.method;
  const path = c.req.path;
  const headers = Object.fromEntries([...c.req.raw.headers.entries()]);

  console.log(`Unhandled request: ${method} ${path}`);
  console.log("Headers:", headers);

  return c.json({
    error: "Not found",
    message: `No handler for ${method} ${path}`,
    requestInfo: {
      method,
      path,
      headers: {
        ...headers,
        // Remove sensitive headers
        authorization: headers.authorization ? "[REDACTED]" : undefined,
        cookie: headers.cookie ? "[REDACTED]" : undefined,
      }
    }
  }, 404);
});

export { app };